
import { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Package, AlertTriangle, Search, Filter } from 'lucide-react';
import { Product, Category } from '../../types/inventory';
import { getCategories } from '../../services/inventoryService';
import { useSyncedProducts, useSyncedCategories, useProductOperations } from '../../hooks/useSyncedData';
import { formatPrice, getStockStatusColor, getStockStatusText } from '../../utils/inventoryUtils';
import ProductForm from './ProductForm';
import ImageService from '../../services/imageService';

const ProductManagement = () => {
  // Use synchronized data hooks
  const { data: products, loading, error, refetch } = useSyncedProducts();
  const { data: categories } = useSyncedCategories();
  const { createProduct, updateProduct, deleteProduct, loading: operationLoading, error: operationError } = useProductOperations();

  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [showLowStock, setShowLowStock] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [showForm, setShowForm] = useState(false);

  useEffect(() => {
    console.log('ProductManagement: Products data updated:', products);
    filterProducts();
  }, [products, searchTerm, selectedCategory, showLowStock]);

  const filterProducts = () => {
    if (!products || !Array.isArray(products)) {
      setFilteredProducts([]);
      return;
    }

    let filtered = [...products];

    if (searchTerm) {
      filtered = filtered.filter(product =>
        product.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.brand.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (selectedCategory) {
      filtered = filtered.filter(product => product.category === selectedCategory);
    }

    if (showLowStock) {
      filtered = filtered.filter(product => product.stock <= product.minStock);
    }

    setFilteredProducts(filtered);
  };

  const handleSaveProduct = async (productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      if (selectedProduct) {
        console.log('Updating product:', selectedProduct.id, productData);

        // Handle category properly - check if it's a UUID or a name
        let categoryId = productData.category;
        const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(productData.category);

        if (!isUUID) {
          // It's a predefined category name, try to find the corresponding category ID
          const category = categories.find(cat => cat.name === productData.category);
          if (category) {
            categoryId = category.id;
          } else {
            // If no matching category found, we need to create one or handle as string
            console.warn('Category not found in database:', productData.category);
            categoryId = productData.category; // Keep as string for now
          }
        }

        const result = await updateProduct(selectedProduct.id, {
          title: productData.title,
          description: productData.description,
          brand: productData.brand,
          price: productData.price,
          stock: productData.stock,
          min_stock: productData.minStock,
          category_id: categoryId,
          featured_image: productData.featuredImage,
          sku: productData.sku
        });

        if (result.success) {
          setShowForm(false);
          setSelectedProduct(null);
          // Trigger a refetch to ensure UI is updated with latest data
          refetch();
          alert('Product updated successfully!');
        } else {
          console.error('Update failed:', result.error);
          alert(result.error || 'Failed to update product');
        }
      } else {
        console.log('Creating new product:', productData);

        // Handle category properly for new products too
        let categoryId = productData.category;
        const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(productData.category);

        if (!isUUID) {
          // It's a predefined category name, try to find the corresponding category ID
          const category = categories.find(cat => cat.name === productData.category);
          if (category) {
            categoryId = category.id;
          } else {
            console.warn('Category not found in database:', productData.category);
            categoryId = productData.category; // Keep as string for now
          }
        }

        const result = await createProduct({
          title: productData.title,
          description: productData.description,
          brand: productData.brand,
          price: productData.price,
          stock: productData.stock,
          min_stock: productData.minStock,
          category_id: categoryId,
          featured_image: productData.featuredImage,
          sku: productData.sku
        });

        if (result.success) {
          setShowForm(false);
          setSelectedProduct(null);
          // Trigger a refetch to ensure UI is updated with latest data
          refetch();
          alert('Product created successfully!');
        } else {
          console.error('Create failed:', result.error);
          alert(result.error || 'Failed to create product');
        }
      }
    } catch (error) {
      console.error('Error saving product:', error);
      alert('Error saving product: ' + (error as Error).message);
    }
  };

  const handleDeleteProduct = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this product? This action cannot be undone.')) {
      try {
        console.log('Deleting product:', id);
        const result = await deleteProduct(id);

        if (result.success) {
          // Trigger a refetch to ensure UI is updated with latest data
          refetch();
          alert('Product deleted successfully!');
        } else {
          console.error('Delete failed:', result.error);
          alert(result.error || 'Failed to delete product');
        }
      } catch (error) {
        console.error('Error deleting product:', error);
        alert('Error deleting product: ' + (error as Error).message);
      }
    }
  };

  const lowStockCount = products.filter(p => p.stock <= p.minStock).length;

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Package className="h-8 w-8 text-gray-400 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Product Management</h2>
          <p className="text-gray-600">{products.length} products total</p>
        </div>
        <button
          onClick={() => {
            setSelectedProduct(null);
            setShowForm(true);
          }}
          className="bg-gradient-to-r from-teal-600 to-teal-700 text-white px-4 py-2 rounded-lg hover:from-teal-700 hover:to-teal-800 transition-all duration-200 flex items-center space-x-2"
        >
          <Plus className="h-4 w-4" />
          <span>Add Product</span>
        </button>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Products</p>
              <p className="text-xl font-bold text-gray-900">{products.length}</p>
            </div>
            <Package className="h-8 w-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Low Stock Alerts</p>
              <p className="text-xl font-bold text-red-600">{lowStockCount}</p>
            </div>
            <AlertTriangle className="h-8 w-8 text-red-500" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Active Products</p>
              <p className="text-xl font-bold text-green-600">{products.filter(p => p.isActive).length}</p>
            </div>
            <Package className="h-8 w-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Categories</p>
              <p className="text-xl font-bold text-purple-600">{categories.length}</p>
            </div>
            <Filter className="h-8 w-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search products..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
            />
          </div>

          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
          >
            <option value="">All Categories</option>
            {categories.map(category => (
              <option key={category.id} value={category.name}>{category.name}</option>
            ))}
          </select>

          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={showLowStock}
              onChange={(e) => setShowLowStock(e.target.checked)}
              className="rounded border-gray-300 text-teal-600 focus:ring-teal-500"
            />
            <span className="text-sm text-gray-700">Low Stock Only</span>
          </label>
        </div>
      </div>

      {/* Products Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SKU</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredProducts.map(product => (
                <tr key={product.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <img
                          src={ImageService.getBestImageUrl(product, 40)}
                          alt={product.title}
                          className="h-10 w-10 rounded-lg object-cover border border-gray-200"
                          onError={(e) => {
                            e.currentTarget.src = ImageService.generateFallbackImage(product.title, 40);
                          }}
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium text-gray-900 truncate">
                          {product.title}
                        </div>
                        <div className="text-sm text-gray-500 truncate">
                          {product.brand || 'No brand'}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{product.sku}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{product.category}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatPrice(product.price)}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{product.stock} units</div>
                    <div className="text-xs text-gray-500">Min: {product.minStock}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStockStatusColor(product.stock, product.minStock)}`}>
                      {getStockStatusText(product.stock, product.minStock)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => {
                          setSelectedProduct(product);
                          setShowForm(true);
                        }}
                        className="text-teal-600 hover:text-teal-900"
                        title="Edit Product"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteProduct(product.id)}
                        className="text-red-600 hover:text-red-900"
                        title="Delete Product"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {filteredProducts.length === 0 && (
          <div className="text-center py-8">
            <Package className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No products found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm || selectedCategory || showLowStock 
                ? 'Try adjusting your filters' 
                : 'Get started by adding a new product'
              }
            </p>
          </div>
        )}
      </div>

      {/* Product Form Modal */}
      {showForm && (
        <ProductForm
          product={selectedProduct}
          categories={categories}
          onSave={handleSaveProduct}
          onCancel={() => {
            setShowForm(false);
            setSelectedProduct(null);
          }}
        />
      )}
    </div>
  );
};

export default ProductManagement;
