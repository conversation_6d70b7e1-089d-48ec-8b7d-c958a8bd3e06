
import { useState } from 'react';
import { LogOut, Bell, User, Menu } from 'lucide-react';
import AdminDashboard from './dashboards/AdminDashboard';
import ManagerDashboard from './dashboards/ManagerDashboard';
import DeliveryDashboard from './dashboards/DeliveryDashboard';
import ClientDashboard from './dashboards/ClientDashboard';
import NotificationSystem from './notifications/NotificationSystem';
import NotificationBell from './ui/NotificationBell';

interface DashboardProps {
  user: any;
  onLogout: () => void;
  onNavigateToProfile?: () => void;
}

const Dashboard = ({ user, onLogout, onNavigateToProfile }: DashboardProps) => {
  const [showNotifications, setShowNotifications] = useState(false);
  const [showProfile, setShowProfile] = useState(false);
  const [showMobileMenu, setShowMobileMenu] = useState(false);
  const [dashboardNavigate, setDashboardNavigate] = useState<((page: string) => void) | null>(null);



  const renderDashboard = () => {
    // Debug logging to check user type
    console.log('Dashboard renderDashboard - User:', user);
    console.log('Dashboard renderDashboard - User Type:', user.userType);

    switch (user.userType) {
      case 'admin':
        console.log('Rendering AdminDashboard');
        return <AdminDashboard user={user} onNavigateFromHeader={setDashboardNavigate} />;
      case 'manager':
        console.log('Rendering ManagerDashboard');
        return <ManagerDashboard user={user} onNavigateFromHeader={setDashboardNavigate} />;
      case 'delivery':
      case 'delivery_person':
        console.log('Rendering DeliveryDashboard');
        return <DeliveryDashboard user={user} onNavigateFromHeader={setDashboardNavigate} />;
      case 'client':
      case 'reseller':
        console.log('Rendering ClientDashboard');
        return <ClientDashboard user={user} onNavigateFromHeader={setDashboardNavigate} />;
      default:
        console.log('Rendering default ClientDashboard for user type:', user.userType);
        return <ClientDashboard user={user} onNavigateFromHeader={setDashboardNavigate} />;
    }
  };

  // Close mobile dropdowns when clicking outside
  const handleOutsideClick = () => {
    setShowNotifications(false);
    setShowProfile(false);
    setShowMobileMenu(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-teal-50 via-white to-orange-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900" onClick={handleOutsideClick}>
      {/* Header - Mobile Optimized */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-teal-100 dark:border-gray-700 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-14 sm:h-16">
            {/* Logo and Title - Responsive */}
            <div className="flex items-center space-x-2 sm:space-x-3">
              <div className="bg-gradient-to-r from-teal-600 to-teal-700 p-1.5 sm:p-2 rounded-lg">
                <div className="h-6 w-6 sm:h-8 sm:w-8 bg-white rounded text-teal-600 flex items-center justify-center font-bold text-sm sm:text-base">
                  Y
                </div>
              </div>
              <div className="hidden sm:block">
                <h1 className="text-lg sm:text-xl font-bold text-gray-900 dark:text-white">YalaOffice</h1>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-300 capitalize">{user.userType} Dashboard</p>
              </div>
              {/* Mobile Title */}
              <div className="sm:hidden">
                <h1 className="text-lg font-bold text-gray-900 dark:text-white">YalaOffice</h1>
              </div>
            </div>

            {/* Mobile Menu Button */}
            <div className="flex items-center space-x-2 sm:space-x-4">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setShowMobileMenu(!showMobileMenu);
                }}
                className="sm:hidden p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                <Menu className="h-5 w-5 text-gray-600 dark:text-gray-300" />
              </button>

              {/* Desktop Navigation */}
              <div className="hidden sm:flex items-center space-x-4">
                {/* Notifications */}
                <NotificationBell userId={user.id} className="text-gray-600 dark:text-gray-300" />

                {/* Profile */}
                <div className="relative">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowProfile(!showProfile);
                    }}
                    className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  >
                    <div className="bg-teal-600 text-white rounded-full h-6 w-6 sm:h-8 sm:w-8 flex items-center justify-center">
                      <User className="h-3 w-3 sm:h-4 sm:w-4" />
                    </div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300 hidden lg:block">{user.fullName}</span>
                  </button>

                  {showProfile && (
                    <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 z-50" onClick={(e) => e.stopPropagation()}>
                      <div className="p-2">
                        <button
                          onClick={() => {
                            // Use dashboard navigation if available, otherwise fallback
                            if (dashboardNavigate) {
                              dashboardNavigate('profile');
                            } else if (onNavigateToProfile) {
                              onNavigateToProfile();
                            }
                            setShowProfile(false);
                          }}
                          className="flex items-center space-x-2 w-full p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                        >
                          <User className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                          <span className="text-sm text-gray-700 dark:text-gray-300">Profile</span>
                        </button>
                        <button
                          onClick={onLogout}
                          className="flex items-center space-x-2 w-full p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-red-600 dark:text-red-400"
                        >
                          <LogOut className="h-4 w-4" />
                          <span className="text-sm">Logout</span>
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Mobile Navigation Menu */}
          {showMobileMenu && (
            <div className="sm:hidden border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800" onClick={(e) => e.stopPropagation()}>
              <div className="py-2 space-y-1">
                {/* User Info */}
                <div className="px-4 py-2 border-b border-gray-100 dark:border-gray-600">
                  <div className="flex items-center space-x-3">
                    <div className="bg-teal-600 text-white rounded-full h-8 w-8 flex items-center justify-center">
                      <User className="h-4 w-4" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">{user.fullName}</p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 capitalize">{user.userType}</p>
                    </div>
                  </div>
                </div>

                {/* Mobile Notifications */}
                <button
                  onClick={() => setShowNotifications(!showNotifications)}
                  className="flex items-center justify-between w-full px-4 py-2 text-left hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  <div className="flex items-center space-x-3">
                    <Bell className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                    <span className="text-sm text-gray-700 dark:text-gray-300">Notifications</span>
                  </div>
                  <span className="bg-orange-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    3
                  </span>
                </button>

                {/* Mobile Settings */}
                <button className="flex items-center space-x-3 w-full px-4 py-2 text-left hover:bg-gray-50 dark:hover:bg-gray-700">
                  <Settings className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Settings</span>
                </button>

                {/* Mobile Logout */}
                <button
                  onClick={onLogout}
                  className="flex items-center space-x-3 w-full px-4 py-2 text-left hover:bg-gray-50 dark:hover:bg-gray-700 text-red-600 dark:text-red-400"
                >
                  <LogOut className="h-5 w-5" />
                  <span className="text-sm">Logout</span>
                </button>
              </div>
            </div>
          )}
        </div>
      </header>

      {/* Dashboard Content - Mobile Optimized */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-8">
        {renderDashboard()}
      </main>

      {/* Real-time Notification System */}
      <NotificationSystem
        userType={user.userType}
        userId={user.id}
      />
    </div>
  );
};

export default Dashboard;
