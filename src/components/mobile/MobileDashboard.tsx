import React, { useState, useEffect } from 'react';
import {
  TrendingUp,
  TrendingDown,
  Package,
  ShoppingCart,
  Users,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  Clock,
  Eye,
  Plus,
  ArrowRight
} from 'lucide-react';

interface MobileDashboardProps {
  onNavigate: (page: string) => void;
}

interface DashboardStats {
  revenue: { value: number; change: number; trend: 'up' | 'down' };
  orders: { value: number; change: number; trend: 'up' | 'down' };
  customers: { value: number; change: number; trend: 'up' | 'down' };
  products: { value: number; change: number; trend: 'up' | 'down' };
}

interface RecentActivity {
  id: string;
  type: 'order' | 'customer' | 'product' | 'alert';
  title: string;
  description: string;
  time: string;
  status: 'success' | 'warning' | 'error' | 'info';
}

const MobileDashboard: React.FC<MobileDashboardProps> = ({ onNavigate }) => {
  const [stats, setStats] = useState<DashboardStats>({
    revenue: { value: 45280, change: 12.5, trend: 'up' },
    orders: { value: 156, change: -3.2, trend: 'down' },
    customers: { value: 1247, change: 8.7, trend: 'up' },
    products: { value: 892, change: 2.1, trend: 'up' }
  });

  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([
    {
      id: '1',
      type: 'order',
      title: 'New Order #ORD-1234',
      description: 'Ahmed Mansouri - 2,450 MAD',
      time: '5 min ago',
      status: 'success'
    },
    {
      id: '2',
      type: 'alert',
      title: 'Low Stock Alert',
      description: 'Office Chair - Only 3 left',
      time: '15 min ago',
      status: 'warning'
    },
    {
      id: '3',
      type: 'customer',
      title: 'New Customer',
      description: 'Fatima El Amrani registered',
      time: '1 hour ago',
      status: 'info'
    },
    {
      id: '4',
      type: 'product',
      title: 'Product Updated',
      description: 'Desk Lamp - Price changed',
      time: '2 hours ago',
      status: 'info'
    }
  ]);

  const quickActions = [
    {
      id: 'new-order',
      title: 'New Order',
      description: 'Create a new order',
      icon: ShoppingCart,
      color: 'bg-blue-500',
      action: () => onNavigate('orders/new')
    },
    {
      id: 'add-product',
      title: 'Add Product',
      description: 'Add new inventory',
      icon: Package,
      color: 'bg-green-500',
      action: () => onNavigate('inventory/new')
    },
    {
      id: 'add-customer',
      title: 'Add Customer',
      description: 'Register new customer',
      icon: Users,
      color: 'bg-purple-500',
      action: () => onNavigate('customers/new')
    },
    {
      id: 'view-analytics',
      title: 'Analytics',
      description: 'View reports',
      icon: TrendingUp,
      color: 'bg-orange-500',
      action: () => onNavigate('analytics')
    }
  ];

  const getActivityIcon = (type: string, status: string) => {
    switch (type) {
      case 'order':
        return <ShoppingCart className="h-4 w-4" />;
      case 'customer':
        return <Users className="h-4 w-4" />;
      case 'product':
        return <Package className="h-4 w-4" />;
      case 'alert':
        return <AlertTriangle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'text-green-600 bg-green-100';
      case 'warning':
        return 'text-yellow-600 bg-yellow-100';
      case 'error':
        return 'text-red-600 bg-red-100';
      case 'info':
        return 'text-blue-600 bg-blue-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  return (
    <div className="pb-20"> {/* Space for bottom navigation */}
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 rounded-b-3xl mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Welcome back!</h1>
            <p className="text-blue-100 mt-1">Here's what's happening today</p>
          </div>
          <div className="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
            <TrendingUp className="h-6 w-6" />
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 gap-4 px-4 mb-6">
        <div className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between mb-2">
            <div className="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center">
              <DollarSign className="h-5 w-5 text-green-600" />
            </div>
            <div className={`flex items-center space-x-1 ${
              stats.revenue.trend === 'up' ? 'text-green-600' : 'text-red-600'
            }`}>
              {stats.revenue.trend === 'up' ? (
                <TrendingUp className="h-3 w-3" />
              ) : (
                <TrendingDown className="h-3 w-3" />
              )}
              <span className="text-xs font-medium">{Math.abs(stats.revenue.change)}%</span>
            </div>
          </div>
          <p className="text-2xl font-bold text-gray-900">{formatNumber(stats.revenue.value)}</p>
          <p className="text-xs text-gray-600">Revenue (MAD)</p>
        </div>

        <div className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between mb-2">
            <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
              <ShoppingCart className="h-5 w-5 text-blue-600" />
            </div>
            <div className={`flex items-center space-x-1 ${
              stats.orders.trend === 'up' ? 'text-green-600' : 'text-red-600'
            }`}>
              {stats.orders.trend === 'up' ? (
                <TrendingUp className="h-3 w-3" />
              ) : (
                <TrendingDown className="h-3 w-3" />
              )}
              <span className="text-xs font-medium">{Math.abs(stats.orders.change)}%</span>
            </div>
          </div>
          <p className="text-2xl font-bold text-gray-900">{stats.orders.value}</p>
          <p className="text-xs text-gray-600">Orders</p>
        </div>

        <div className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between mb-2">
            <div className="w-10 h-10 bg-purple-100 rounded-xl flex items-center justify-center">
              <Users className="h-5 w-5 text-purple-600" />
            </div>
            <div className={`flex items-center space-x-1 ${
              stats.customers.trend === 'up' ? 'text-green-600' : 'text-red-600'
            }`}>
              {stats.customers.trend === 'up' ? (
                <TrendingUp className="h-3 w-3" />
              ) : (
                <TrendingDown className="h-3 w-3" />
              )}
              <span className="text-xs font-medium">{Math.abs(stats.customers.change)}%</span>
            </div>
          </div>
          <p className="text-2xl font-bold text-gray-900">{formatNumber(stats.customers.value)}</p>
          <p className="text-xs text-gray-600">Customers</p>
        </div>

        <div className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between mb-2">
            <div className="w-10 h-10 bg-orange-100 rounded-xl flex items-center justify-center">
              <Package className="h-5 w-5 text-orange-600" />
            </div>
            <div className={`flex items-center space-x-1 ${
              stats.products.trend === 'up' ? 'text-green-600' : 'text-red-600'
            }`}>
              {stats.products.trend === 'up' ? (
                <TrendingUp className="h-3 w-3" />
              ) : (
                <TrendingDown className="h-3 w-3" />
              )}
              <span className="text-xs font-medium">{Math.abs(stats.products.change)}%</span>
            </div>
          </div>
          <p className="text-2xl font-bold text-gray-900">{stats.products.value}</p>
          <p className="text-xs text-gray-600">Products</p>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="px-4 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Quick Actions</h2>
        </div>
        <div className="grid grid-cols-2 gap-3">
          {quickActions.map((action) => {
            const Icon = action.icon;
            return (
              <button
                key={action.id}
                onClick={action.action}
                className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 hover:shadow-md transition-shadow"
              >
                <div className={`w-12 h-12 ${action.color} rounded-xl flex items-center justify-center mb-3`}>
                  <Icon className="h-6 w-6 text-white" />
                </div>
                <h3 className="font-semibold text-gray-900 text-sm mb-1">{action.title}</h3>
                <p className="text-xs text-gray-600">{action.description}</p>
              </button>
            );
          })}
        </div>
      </div>

      {/* Recent Activity */}
      <div className="px-4 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Recent Activity</h2>
          <button
            onClick={() => onNavigate('activity')}
            className="text-blue-600 text-sm font-medium flex items-center space-x-1"
          >
            <span>View All</span>
            <ArrowRight className="h-3 w-3" />
          </button>
        </div>
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
          {recentActivity.map((activity, index) => (
            <div
              key={activity.id}
              className={`p-4 flex items-center space-x-3 ${
                index !== recentActivity.length - 1 ? 'border-b border-gray-100' : ''
              }`}
            >
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${getStatusColor(activity.status)}`}>
                {getActivityIcon(activity.type, activity.status)}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">{activity.title}</p>
                <p className="text-xs text-gray-600 truncate">{activity.description}</p>
              </div>
              <div className="text-xs text-gray-500">{activity.time}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Performance Summary */}
      <div className="px-4 mb-6">
        <div className="bg-gradient-to-r from-green-500 to-teal-600 rounded-2xl p-6 text-white">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Today's Performance</h3>
            <CheckCircle className="h-6 w-6" />
          </div>
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold">23</p>
              <p className="text-xs text-green-100">Orders</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold">12.5K</p>
              <p className="text-xs text-green-100">Revenue</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold">8</p>
              <p className="text-xs text-green-100">New Customers</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MobileDashboard;
