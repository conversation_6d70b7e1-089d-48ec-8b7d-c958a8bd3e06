
import { useState, useEffect } from 'react';
import { getLowStockAlerts } from '../../services/inventoryService';
import { LowStockAlert } from '../../types/inventory';
import { liveDashboardService, BranchStats } from '../../services/liveDashboardService';
import DashboardHeader from './components/DashboardHeader';
import NavigationTabs from './components/NavigationTabs';
import QuickStats from './components/QuickStats';
import TabContent from './components/TabContent';
import ProfileManagement from '../profile/ProfileManagement';
import PromoCodeManagement from '../promoCode/PromoCodeManagement';
import PageLayout from '../layout/PageLayout';
import ProductManagement from '../inventory/ProductManagement';
import CategoryManagement from '../inventory/CategoryManagement';
import BranchManagement from '../branches/BranchManagement';
import ClientManagement from '../clients/ClientManagement';

interface ManagerDashboardProps {
  user: any;
  onNavigateFromHeader?: (navigateFunction: (page: string) => void) => void;
}

const ManagerDashboard = ({ user: initialUser, onNavigateFromHeader }: ManagerDashboardProps) => {
  const [user, setUser] = useState(initialUser);

  // Initialize state from URL parameters and localStorage
  const getInitialPage = () => {
    const urlParams = new URLSearchParams(window.location.search);
    const urlPage = urlParams.get('page');
    const storedPage = localStorage.getItem('manager_current_page');
    return urlPage || storedPage || 'dashboard';
  };

  const getInitialTab = () => {
    const urlParams = new URLSearchParams(window.location.search);
    const urlTab = urlParams.get('tab');
    const storedTab = localStorage.getItem('manager_active_tab');
    return urlTab || storedTab || 'inventory';
  };

  const [activeTab, setActiveTab] = useState(getInitialTab());
  const [currentPage, setCurrentPage] = useState(getInitialPage());
  const [lowStockAlerts, setLowStockAlerts] = useState<LowStockAlert[]>([]);
  const [branchStats, setBranchStats] = useState<BranchStats | null>(null);
  const [statsLoading, setStatsLoading] = useState(true);

  useEffect(() => {
    loadLowStockAlerts();
    loadBranchStats();
  }, []);

  const loadBranchStats = async () => {
    try {
      setStatsLoading(true);
      const stats = await liveDashboardService.getBranchDashboardStats();
      setBranchStats(stats);
    } catch (error) {
      console.error('Error loading branch stats:', error);
    } finally {
      setStatsLoading(false);
    }
  };

  const loadLowStockAlerts = async () => {
    try {
      const alerts = await getLowStockAlerts();
      setLowStockAlerts(alerts);
    } catch (error) {
      console.error('Error loading low stock alerts:', error);
    }
  };

  const handleUserUpdate = (updatedUser: any) => {
    setUser(updatedUser);
  };

  const handleNavigate = (page: string, tab?: string) => {
    setCurrentPage(page);

    // Update URL parameters
    const url = new URL(window.location.href);
    url.searchParams.set('page', page);

    if (page === 'dashboard') {
      const newTab = tab || 'inventory';
      setActiveTab(newTab);
      url.searchParams.set('tab', newTab);
      localStorage.setItem('manager_active_tab', newTab);
    } else {
      url.searchParams.delete('tab');
    }

    // Update URL without page reload
    window.history.replaceState({}, '', url.toString());

    // Store in localStorage for persistence
    localStorage.setItem('manager_current_page', page);
  };

  // Provide navigation function to header
  useEffect(() => {
    if (onNavigateFromHeader) {
      onNavigateFromHeader(handleNavigate);
    }
  }, [onNavigateFromHeader]);

  // Use live branch stats or fallback to loading state
  const displayStats = branchStats || {
    totalProducts: 0,
    lowStockItems: lowStockAlerts.length,
    pendingOrders: 0,
    todaysSales: 0
  };

  // Render different pages based on currentPage
  const renderPage = () => {
    switch (currentPage) {
      case 'product-management':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Product Management"
            subtitle="Manage products, inventory, and pricing"
            breadcrumbs={[{ label: 'Store Management', page: 'dashboard' }]}
          >
            <ProductManagement />
          </PageLayout>
        );

      case 'category-management':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Category Management"
            subtitle="Organize products into categories"
            breadcrumbs={[{ label: 'Store Management', page: 'dashboard' }]}
          >
            <CategoryManagement />
          </PageLayout>
        );

      case 'branch-management':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Branch Management"
            subtitle="Manage store branches and locations"
            breadcrumbs={[{ label: 'Store Management', page: 'dashboard' }]}
          >
            <BranchManagement />
          </PageLayout>
        );

      case 'client-management':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Client Management"
            subtitle="Manage clients and resellers"
            breadcrumbs={[{ label: 'Store Management', page: 'dashboard' }]}
          >
            <ClientManagement currentUserId={user.id} userRole="manager" />
          </PageLayout>
        );

      case 'promo-codes':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Promo Code Management"
            subtitle="Create and manage promotional codes"
            breadcrumbs={[{ label: 'Store Management', page: 'dashboard' }]}
          >
            <PromoCodeManagement />
          </PageLayout>
        );

      case 'profile':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Profile Management"
            subtitle="Manage your account settings"
          >
            <ProfileManagement user={user} onUserUpdate={handleUserUpdate} />
          </PageLayout>
        );

      default:
        // Dashboard view with tabs
        return (
          <div className="space-y-6">
            <DashboardHeader userFullName={user.fullName} />

            <NavigationTabs
              activeTab={activeTab}
              onTabChange={setActiveTab}
              userRole="manager"
            />

            {!['profile', 'promo-codes', 'product-management', 'category-management', 'branch-management'].includes(activeTab) && (
              <QuickStats stats={displayStats} loading={statsLoading} />
            )}

            {activeTab === 'profile' ? (
              <ProfileManagement user={user} onUserUpdate={handleUserUpdate} />
            ) : activeTab === 'promo-codes' ? (
              <PromoCodeManagement />
            ) : (
              <TabContent
                activeTab={activeTab}
                lowStockAlerts={lowStockAlerts}
                onRefreshLowStock={loadLowStockAlerts}
                userRole="manager"
                user={user}
                onNavigate={handleNavigate}
              />
            )}
          </div>
        );
    }
  };

  return renderPage();
};

export default ManagerDashboard;
