
import { useState, useEffect } from 'react';
import { Building2, Package, ArrowRightLeft, TrendingUp, MapPin, Clock, Phone, Mail, Plus, Edit, Trash2, Eye } from 'lucide-react';
import { Branch, BranchInventory, StockTransfer, BranchPerformance } from '../../types/branch';
import { 
  getBranches, 
  getBranchInventory, 
  getStockTransfers, 
  getBranchPerformance,
  createBranch,
  updateBranch,
  deleteBranch
} from '../../services/branchService';
import StockTransferModal from './StockTransferModal';
import BranchPerformanceChart from './BranchPerformanceChart';
import BranchFormModal from './BranchFormModal';

interface BranchManagementProps {
  currentUserId?: string;
}

const BranchManagement = ({ currentUserId = 'USR-001' }: BranchManagementProps) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [branches, setBranches] = useState<Branch[]>([]);
  const [selectedBranch, setSelectedBranch] = useState<string>('');
  const [branchInventory, setBranchInventory] = useState<BranchInventory[]>([]);
  const [stockTransfers, setStockTransfers] = useState<StockTransfer[]>([]);
  const [branchPerformance, setBranchPerformance] = useState<BranchPerformance[]>([]);
  const [showTransferModal, setShowTransferModal] = useState(false);
  const [showBranchModal, setShowBranchModal] = useState(false);
  const [editingBranch, setEditingBranch] = useState<Branch | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    if (selectedBranch) {
      loadBranchData(selectedBranch);
    }
  }, [selectedBranch]);

  const loadData = async () => {
    try {
      const [branchesData, transfersData, performanceData] = await Promise.all([
        getBranches(),
        getStockTransfers(),
        getBranchPerformance()
      ]);
      
      setBranches(branchesData);
      setStockTransfers(transfersData);
      setBranchPerformance(performanceData);
      
      if (branchesData.length > 0) {
        setSelectedBranch(branchesData[0].id);
      }
    } catch (error) {
      console.error('Error loading branch data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadBranchData = async (branchId: string) => {
    try {
      const inventoryData = await getBranchInventory(branchId);
      setBranchInventory(inventoryData);
    } catch (error) {
      console.error('Error loading branch inventory:', error);
    }
  };

  const handleCreateBranch = async (branchData: Omit<Branch, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      await createBranch(branchData, currentUserId);
      await loadData();
      setShowBranchModal(false);
    } catch (error) {
      console.error('Error creating branch:', error);
    }
  };

  const handleUpdateBranch = async (branchData: Omit<Branch, 'id' | 'createdAt' | 'updatedAt'>) => {
    if (!editingBranch) return;
    try {
      await updateBranch(editingBranch.id, branchData, currentUserId);
      await loadData();
      setShowBranchModal(false);
      setEditingBranch(null);
    } catch (error) {
      console.error('Error updating branch:', error);
    }
  };

  const handleDeleteBranch = async (branchId: string) => {
    if (window.confirm('Are you sure you want to delete this branch? This action cannot be undone.')) {
      try {
        await deleteBranch(branchId, currentUserId);
        await loadData();
        if (selectedBranch === branchId && branches.length > 1) {
          const remainingBranches = branches.filter(b => b.id !== branchId);
          setSelectedBranch(remainingBranches[0]?.id || '');
        }
      } catch (error) {
        console.error('Error deleting branch:', error);
      }
    }
  };

  const openEditModal = (branch: Branch) => {
    setEditingBranch(branch);
    setShowBranchModal(true);
  };

  const openAddModal = () => {
    setEditingBranch(null);
    setShowBranchModal(true);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Building2 className="h-8 w-8 text-gray-400 animate-spin" />
      </div>
    );
  }

  const selectedBranchData = branches.find(b => b.id === selectedBranch);
  const pendingTransfers = stockTransfers.filter(st => st.status === 'pending').length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Branch Management</h2>
          <p className="text-gray-600">{branches.length} active branches</p>
        </div>
        <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
          <select
            value={selectedBranch}
            onChange={(e) => setSelectedBranch(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
          >
            {branches.map(branch => (
              <option key={branch.id} value={branch.id}>{branch.name}</option>
            ))}
          </select>
          <button
            onClick={openAddModal}
            className="bg-gradient-to-r from-green-600 to-green-700 text-white px-4 py-2 rounded-lg hover:from-green-700 hover:to-green-800 transition-all duration-200 flex items-center space-x-2 justify-center"
          >
            <Plus className="h-4 w-4" />
            <span className="hidden sm:inline">Add Branch</span>
          </button>
          <button
            onClick={() => setShowTransferModal(true)}
            className="bg-gradient-to-r from-teal-600 to-teal-700 text-white px-4 py-2 rounded-lg hover:from-teal-700 hover:to-teal-800 transition-all duration-200 flex items-center space-x-2 justify-center"
          >
            <ArrowRightLeft className="h-4 w-4" />
            <span className="hidden sm:inline">New Transfer</span>
          </button>
        </div>
      </div>

      {/* Branch Info Card - Mobile Optimized */}
      {selectedBranchData && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 sm:p-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div className="flex items-center space-x-3 mb-4 sm:mb-0">
              <div className="bg-teal-100 p-3 rounded-lg">
                <Building2 className="h-6 w-6 text-teal-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">{selectedBranchData.name}</h3>
                <p className="text-sm text-gray-500">Code: {selectedBranchData.code}</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={() => openEditModal(selectedBranchData)}
                className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors"
                title="Edit Branch"
              >
                <Edit className="h-4 w-4" />
              </button>
              <button
                onClick={() => handleDeleteBranch(selectedBranchData.id)}
                className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors"
                title="Delete Branch"
              >
                <Trash2 className="h-4 w-4" />
              </button>
            </div>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm mt-4">
            <div className="flex items-center space-x-2">
              <MapPin className="h-4 w-4 text-gray-400" />
              <span className="text-gray-600">{selectedBranchData.address.city}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-gray-400" />
              <span className="text-gray-600">
                {selectedBranchData.operatingHours.open} - {selectedBranchData.operatingHours.close}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Phone className="h-4 w-4 text-gray-400" />
              <span className="text-gray-600">{selectedBranchData.phone}</span>
            </div>
          </div>
        </div>
      )}

      {/* Quick Stats - Mobile Responsive Grid */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs sm:text-sm text-gray-600">Total Branches</p>
              <p className="text-lg sm:text-xl font-bold text-gray-900">{branches.length}</p>
            </div>
            <Building2 className="h-6 w-6 sm:h-8 sm:w-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs sm:text-sm text-gray-600">Pending Transfers</p>
              <p className="text-lg sm:text-xl font-bold text-orange-600">{pendingTransfers}</p>
            </div>
            <ArrowRightLeft className="h-6 w-6 sm:h-8 sm:w-8 text-orange-500" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs sm:text-sm text-gray-600">Inventory Items</p>
              <p className="text-lg sm:text-xl font-bold text-green-600">{branchInventory.length}</p>
            </div>
            <Package className="h-6 w-6 sm:h-8 sm:w-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs sm:text-sm text-gray-600">Performance</p>
              <p className="text-lg sm:text-xl font-bold text-purple-600">
                {branchPerformance.find(bp => bp.branchId === selectedBranch)?.inventoryTurnover.toFixed(1) || '0.0'}
              </p>
            </div>
            <TrendingUp className="h-6 w-6 sm:h-8 sm:w-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* Navigation Tabs - Mobile Scrollable */}
      <div className="bg-white rounded-lg p-1 shadow-sm border border-gray-200 overflow-x-auto">
        <div className="flex space-x-1 min-w-max sm:min-w-0">
          {[
            { id: 'overview', label: 'Overview', icon: Building2 },
            { id: 'inventory', label: 'Inventory', icon: Package },
            { id: 'transfers', label: 'Transfers', icon: ArrowRightLeft },
            { id: 'performance', label: 'Performance', icon: TrendingUp }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center justify-center space-x-2 py-2 px-3 sm:py-3 sm:px-4 rounded-md transition-all duration-200 whitespace-nowrap ${
                activeTab === tab.id
                  ? 'bg-gradient-to-r from-teal-600 to-teal-700 text-white shadow-md'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <tab.icon className="h-4 w-4 sm:h-5 sm:w-5" />
              <span className="font-medium text-sm sm:text-base">{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {branches.map(branch => (
            <div key={branch.id} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">{branch.name}</h3>
                <div className="flex items-center space-x-2">
                  <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                    branch.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {branch.isActive ? 'Active' : 'Inactive'}
                  </span>
                  <button
                    onClick={() => openEditModal(branch)}
                    className="p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded transition-colors"
                    title="Edit"
                  >
                    <Edit className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleDeleteBranch(branch.id)}
                    className="p-1 text-red-600 hover:text-red-800 hover:bg-red-50 rounded transition-colors"
                    title="Delete"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
              
              <div className="space-y-2 text-sm">
                <div className="flex items-center space-x-2">
                  <MapPin className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-600">{branch.address.street}, {branch.address.city}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Mail className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-600">{branch.email}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-600">
                    {branch.operatingHours.open} - {branch.operatingHours.close}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {activeTab === 'performance' && (
        <BranchPerformanceChart data={branchPerformance} />
      )}

      {/* Branch Form Modal */}
      {showBranchModal && (
        <BranchFormModal
          branch={editingBranch}
          onClose={() => {
            setShowBranchModal(false);
            setEditingBranch(null);
          }}
          onSave={editingBranch ? handleUpdateBranch : handleCreateBranch}
        />
      )}

      {/* Stock Transfer Modal */}
      {showTransferModal && (
        <StockTransferModal
          branches={branches}
          onClose={() => setShowTransferModal(false)}
          onTransferCreated={() => {
            loadData();
            setShowTransferModal(false);
          }}
        />
      )}
    </div>
  );
};

export default BranchManagement;
