import { useState, useEffect } from 'react';
import { Plus, Search, Edit, Trash2, User<PERSON>heck, UserX, Shield, Users, Eye, EyeOff, Download, FileSpreadsheet, Key, Mail, Phone, MapPin, Building, AlertCircle, CheckCircle, RefreshCw, UserPlus, MoreVertical, Check, X, Clock, Ban } from 'lucide-react';
import { User, getUserStats, CreateUserData, getUsers, createUser, updateUser, deleteUser, toggleUserStatus } from '../../services/userManagementService';
import { useSyncedUsers, useUserOperations } from '../../hooks/useSyncedData';
import { exportUsers } from '../../utils/exportUtils';
import { supabase } from '../../integrations/supabase/client';
import { liveDataService } from '../../services/liveDataService';
import { MOROCCAN_CITIES } from '../../constants/cities';

interface UserManagementProps {
  currentUserId: string;
}

// Helper functions for user type display
const getUserTypeLabel = (userType: string | undefined | null): string => {
  if (!userType) {
    return 'Unknown';
  }

  const labels: Record<string, string> = {
    'admin': 'Admin',
    'manager': 'Manager',
    'client': 'Client',
    'reseller': 'Reseller',
    'delivery_person': 'Delivery Person',
    'delivery': 'Delivery Person'
  };

  return labels[userType] || (userType.charAt(0).toUpperCase() + userType.slice(1));
};

const getUserTypeColor = (userType: string | undefined | null): string => {
  if (!userType) {
    return 'bg-gray-100 text-gray-800';
  }

  const colors: Record<string, string> = {
    'admin': 'bg-red-100 text-red-800',
    'manager': 'bg-purple-100 text-purple-800',
    'client': 'bg-blue-100 text-blue-800',
    'reseller': 'bg-green-100 text-green-800',
    'delivery_person': 'bg-orange-100 text-orange-800',
    'delivery': 'bg-orange-100 text-orange-800'
  };

  return colors[userType] || 'bg-gray-100 text-gray-800';
};

const UserManagement = ({ currentUserId }: UserManagementProps) => {
  // Use synchronized data hooks
  const { data: users, loading, error, refetch } = useSyncedUsers();
  const { createUser: createUserSync, updateUser: updateUserSync, deleteUser: deleteUserSync, loading: operationLoading, error: operationError } = useUserOperations();

  // Debug logging
  console.log('UserManagement Debug:', {
    users,
    usersLength: users?.length,
    loading,
    error,
    currentUserId
  });

  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUserType, setSelectedUserType] = useState<'all' | 'admin' | 'manager' | 'client' | 'reseller' | 'delivery_person'>('all');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [showUserForm, setShowUserForm] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);
  const [stats, setStats] = useState({
    total: 0,
    admins: 0,
    managers: 0,
    clients: 0,
    resellers: 0,
    delivery: 0,
    active: 0,
    inactive: 0,
    recentlyCreated: 0
  });

  // Enhanced state for new features
  const [showPasswordModal, setShowPasswordModal] = useState<User | null>(null);
  const [showEmailModal, setShowEmailModal] = useState<User | null>(null);
  const [analyticsLoading, setAnalyticsLoading] = useState(true);
  const [passwordForm, setPasswordForm] = useState({ newPassword: '', confirmPassword: '' });
  const [emailForm, setEmailForm] = useState({ newEmail: '', confirmEmail: '' });

  // Status management state
  const [statusUpdateLoading, setStatusUpdateLoading] = useState<string | null>(null);
  const [showStatusDropdown, setShowStatusDropdown] = useState<string | null>(null);

  // Real-time Supabase integration and statistics loading
  useEffect(() => {
    const initializeUserManagement = async () => {
      // First, ensure current user exists in users table
      await liveDataService.ensureCurrentUserInUsersTable();

      // Then load stats
      loadStats();
    };

    initializeUserManagement();

    // Set up real-time subscription for statistics updates
    const subscription = supabase
      .channel('users_changes')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'users' }, () => {
        loadStats();
        refetch(); // Refresh users data from the hook
      })
      .subscribe();

    return () => {
      supabase.removeChannel(subscription);
    };
  }, [refetch]);

  // Close status dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showStatusDropdown) {
        setShowStatusDropdown(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showStatusDropdown]);

  // Update stats when users change
  useEffect(() => {
    if (users && users.length >= 0) {
      loadStats();
    }
  }, [users]);

  // Update filtered users when users or filters change
  useEffect(() => {
    if (!users) return;

    console.log('UserManagement: Users data received:', users);
    console.log('UserManagement: Sample user data:', users?.[0]);

    let filtered = [...users];

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(user =>
        user.fullName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply user type filter
    if (selectedUserType !== 'all') {
      filtered = filtered.filter(user => user.userType === selectedUserType);
    }

    setFilteredUsers(filtered);
  }, [users, searchTerm, selectedUserType]);

  // Enhanced statistics calculation from real Supabase data
  const loadStats = async () => {
    try {
      setAnalyticsLoading(true);
      console.log('UserManagement: Loading user statistics...');

      // Use the new liveDataService method
      const stats = await liveDataService.getUserStatistics();
      console.log('UserManagement: Received statistics:', stats);

      setStats(stats);
    } catch (error) {
      console.error('Error loading enhanced user stats:', error);
      // Set default stats on error
      setStats({
        total: 0,
        admins: 0,
        managers: 0,
        clients: 0,
        resellers: 0,
        delivery: 0,
        active: 0,
        inactive: 0,
        recentlyCreated: 0
      });
    } finally {
      setAnalyticsLoading(false);
    }
  };

  const filterUsers = () => {
    let filtered = users;

    if (searchTerm) {
      filtered = filtered.filter(user =>
        (user.fullName || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
        (user.email || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
        (user.department || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
        (user.branch || '').toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (selectedUserType !== 'all') {
      filtered = filtered.filter(user => (user.userType || '').toLowerCase() === selectedUserType.toLowerCase());
    }

    setFilteredUsers(filtered);
  };

  // Status management functions
  const handleStatusUpdate = async (userId: string, updates: { is_active?: boolean; is_verified?: boolean; status?: string }) => {
    try {
      setStatusUpdateLoading(userId);
      console.log('UserManagement: Updating user status:', { userId, updates });

      // Use the more robust userManagementService for status updates
      if (updates.is_active !== undefined) {
        const result = await toggleUserStatus(userId, currentUserId);

        if (result) {
          // Refresh the users data
          refetch();

          // Update local state immediately for better UX
          setFilteredUsers(prev => prev.map(user =>
            user.id === userId
              ? { ...user, isActive: result.isActive }
              : user
          ));

          alert(`User ${result.isActive ? 'activated' : 'deactivated'} successfully!`);
        } else {
          alert('Failed to update user status');
        }
      } else {
        // For other status updates, use the liveDataService
        const result = await liveDataService.updateUserStatus(userId, updates);

        if (result.success) {
          // Refresh the users data
          refetch();

          // Update local state immediately for better UX
          setFilteredUsers(prev => prev.map(user =>
            user.id === userId
              ? {
                  ...user,
                  ...(updates.is_verified !== undefined && { isVerified: updates.is_verified })
                }
              : user
          ));

          alert('User status updated successfully!');
        } else {
          console.error('Status update failed:', result.error);
          alert(result.error || 'Failed to update user status');
        }
      }
    } catch (error) {
      console.error('Error updating user status:', error);
      alert('Error updating user status: ' + (error as Error).message);
    } finally {
      setStatusUpdateLoading(null);
      setShowStatusDropdown(null);
    }
  };

  const toggleUserActive = (userId: string, currentStatus: boolean) => {
    console.log('UserManagement: toggleUserActive called:', { userId, currentStatus, newStatus: !currentStatus });
    handleStatusUpdate(userId, { is_active: !currentStatus });
  };

  const toggleUserVerified = (userId: string, currentStatus: boolean) => {
    handleStatusUpdate(userId, { is_verified: !currentStatus });
  };

  const setUserStatus = (userId: string, status: string) => {
    const statusMap = {
      'active': { is_active: true, status: 'active' },
      'inactive': { is_active: false, status: 'inactive' },
      'suspended': { is_active: false, status: 'suspended' },
      'pending': { is_active: false, status: 'pending' }
    };

    const updates = statusMap[status as keyof typeof statusMap];
    if (updates) {
      handleStatusUpdate(userId, updates);
    }
  };

  const handleCreateUser = async (userData: CreateUserData) => {
    try {
      const result = await createUserSync({
        email: userData.email,
        password: userData.password || 'TempPassword2024!',
        full_name: userData.fullName,
        user_type: userData.userType,
        phone: userData.phone,
        city: userData.city || 'Tetouan',
        company_name: userData.companyName,
        company_address: userData.companyAddress
      });

      if (result.success) {
        setShowUserForm(false);
        setSelectedUser(null);

        // Immediately refresh data and statistics
        await Promise.all([
          refetch(), // Refresh users data
          loadStats() // Refresh statistics
        ]);

        alert('User created successfully!');
      } else {
        alert(result.error || 'Failed to create user');
      }
    } catch (error) {
      console.error('Error creating user:', error);
      alert('Error creating user');
    }
  };

  // Password management function
  const handleChangePassword = async (userId: string, newPassword: string) => {
    try {
      // In a real implementation, this would call a secure password update API
      const { error } = await supabase.auth.admin.updateUserById(userId, {
        password: newPassword
      });

      if (error) throw error;

      alert('Password updated successfully!');
      setShowPasswordModal(null);
      setPasswordForm({ newPassword: '', confirmPassword: '' });
    } catch (error) {
      console.error('Error updating password:', error);
      alert('Error updating password');
    }
  };

  // Email management function
  const handleChangeEmail = async (userId: string, newEmail: string) => {
    try {
      const { error } = await supabase
        .from('users')
        .update({ email: newEmail })
        .eq('id', userId);

      if (error) throw error;

      alert('Email updated successfully!');
      setShowEmailModal(null);
      setEmailForm({ newEmail: '', confirmEmail: '' });
      refetch(); // Refresh data
    } catch (error) {
      console.error('Error updating email:', error);
      alert('Error updating email');
    }
  };

  // Account status toggle function
  const handleToggleUserStatus = async (userId: string, currentStatus: boolean) => {
    try {
      const { error } = await supabase
        .from('users')
        .update({ is_active: !currentStatus })
        .eq('id', userId);

      if (error) throw error;

      alert(`User ${!currentStatus ? 'activated' : 'deactivated'} successfully!`);
      refetch(); // Refresh data
    } catch (error) {
      console.error('Error toggling user status:', error);
      alert('Error updating user status');
    }
  };

  // User type change function
  const handleChangeUserType = async (userId: string, newUserType: string) => {
    try {
      const { error } = await supabase
        .from('users')
        .update({ user_type: newUserType })
        .eq('id', userId);

      if (error) throw error;

      alert('User type updated successfully!');
      refetch(); // Refresh data
    } catch (error) {
      console.error('Error updating user type:', error);
      alert('Error updating user type');
    }
  };

  // Export functions
  const handleExportExcel = () => {
    if (filteredUsers.length === 0) {
      alert('No data to export');
      return;
    }
    exportUsers(filteredUsers, 'excel');
  };

  const handleExportCSV = () => {
    if (filteredUsers.length === 0) {
      alert('No data to export');
      return;
    }
    exportUsers(filteredUsers, 'csv');
  };

  const handleUpdateUser = async (userData: any) => {
    if (!selectedUser) return;

    try {
      // Prepare update data
      const updateData: any = {
        full_name: userData.fullName,
        email: userData.email,
        user_type: userData.userType,
        phone: userData.phone,
        city: userData.city,
        company_name: userData.companyName,
        company_address: userData.companyAddress,
        company_phone: userData.companyPhone,
        company_email: userData.companyEmail,
        tax_id: userData.taxId,
        legal_form: userData.legalForm,
        ice_number: userData.iceNumber,
        is_company: userData.isCompany
      };

      // Handle password update if provided
      if (userData.password && userData.password.trim() !== '') {
        // Update password in Supabase Auth
        const { error: authError } = await supabase.auth.admin.updateUserById(
          selectedUser.id,
          { password: userData.password }
        );

        if (authError) {
          console.error('Error updating password:', authError);
          alert('Error updating password: ' + authError.message);
          return;
        }
      }

      // Update user data in database
      const result = await updateUserSync(selectedUser.id, updateData);

      if (result.success) {
        // Refresh the data and statistics to show changes immediately
        await Promise.all([
          refetch(), // Refresh users data
          loadStats() // Refresh statistics
        ]);

        setShowUserForm(false);
        setSelectedUser(null);
        alert('User updated successfully!');
      } else {
        alert(result.error || 'Failed to update user');
      }
    } catch (error) {
      console.error('Error updating user:', error);
      alert('Error updating user: ' + (error as Error).message);
    }
  };

  const handleDeleteUser = async (userId: string) => {
    try {
      const result = await deleteUserSync(userId);

      if (result.success) {
        setShowDeleteConfirm(null);

        // Immediately refresh data and statistics
        await Promise.all([
          refetch(), // Refresh users data
          loadStats() // Refresh statistics
        ]);

        alert('User deleted successfully!');
      } else {
        alert(result.error || 'Failed to delete user');
      }
    } catch (error) {
      console.error('Error deleting user:', error);
      alert('Error deleting user: ' + (error as Error).message);
    }
  };



  // Show loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
        <span className="ml-3 text-gray-600">Loading users...</span>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-red-500 mb-2">Error loading users</div>
          <div className="text-sm text-gray-600 mb-4">{error}</div>
          <button
            onClick={refetch}
            className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // Ensure users is an array
  const safeUsers = Array.isArray(users) ? users : [];
  const safeFilteredUsers = Array.isArray(filteredUsers) ? filteredUsers : [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">User Management</h2>
          <p className="text-gray-600">Manage all user accounts (Admin, Manager, Client, Reseller, Delivery)</p>
        </div>
        <div className="flex items-center space-x-3">
          {/* Export Buttons */}
          <div className="flex items-center space-x-2">
            <button
              onClick={handleExportExcel}
              className="bg-green-600 text-white px-3 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
              title="Export to Excel"
            >
              <FileSpreadsheet className="h-4 w-4" />
              <span className="hidden sm:inline">Excel</span>
            </button>
            <button
              onClick={handleExportCSV}
              className="bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
              title="Export to CSV"
            >
              <Download className="h-4 w-4" />
              <span className="hidden sm:inline">CSV</span>
            </button>
          </div>

          {/* Refresh Button */}
          <button
            onClick={refetch}
            className="bg-gray-600 text-white px-3 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center space-x-2"
            title="Refresh Data"
          >
            <RefreshCw className="h-4 w-4" />
          </button>

          {/* Add User Button */}
          <button
            onClick={() => {
              setSelectedUser(null);
              setShowUserForm(true);
            }}
            className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors flex items-center space-x-2"
          >
            <Plus className="h-5 w-5" />
            <span>Add User</span>
          </button>
        </div>
      </div>

      {/* Enhanced Stats Cards */}
      {analyticsLoading ? (
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
          {[...Array(7)].map((_, index) => (
            <div key={index} className="bg-white rounded-lg border border-gray-200 p-4 animate-pulse">
              <div className="h-4 bg-gray-200 rounded mb-2"></div>
              <div className="h-8 bg-gray-200 rounded"></div>
            </div>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
          {/* Total Users */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-blue-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Total Users</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.total}</p>
              </div>
            </div>
          </div>

          {/* Admins */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center">
              <Shield className="h-8 w-8 text-red-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Admins</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.admins}</p>
              </div>
            </div>
          </div>

          {/* Managers */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-purple-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Managers</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.managers}</p>
              </div>
            </div>
          </div>

          {/* Clients */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center">
              <Building className="h-8 w-8 text-teal-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Clients</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.clients}</p>
              </div>
            </div>
          </div>

          {/* Resellers */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-amber-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Resellers</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.resellers}</p>
              </div>
            </div>
          </div>

          {/* Active Users */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center">
              <UserCheck className="h-8 w-8 text-green-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Active</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.active}</p>
              </div>
            </div>
          </div>

          {/* Recent Users */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center">
              <Plus className="h-8 w-8 text-orange-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">New (7d)</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.recentlyCreated}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search users..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              />
            </div>
          </div>
          
          <select
            value={selectedUserType}
            onChange={(e) => setSelectedUserType(e.target.value as 'all' | 'admin' | 'manager' | 'client' | 'reseller' | 'delivery_person')}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          >
            <option value="all">All User Types</option>
            <option value="admin">Admins</option>
            <option value="manager">Managers</option>
            <option value="client">Clients</option>
            <option value="reseller">Resellers</option>
            <option value="delivery_person">Delivery Personnel</option>
            <option value="client">Clients</option>
            <option value="reseller">Resellers</option>
            <option value="delivery_person">Delivery Personnel</option>
          </select>
        </div>
      </div>

      {/* Users Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Phone Number
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Last Login
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {safeFilteredUsers.length === 0 ? (
                <tr>
                  <td colSpan={6} className="px-6 py-8 text-center text-gray-500">
                    {searchTerm || selectedUserType !== 'all' ? 'No users match your filters' : 'No users found'}
                  </td>
                </tr>
              ) : (
                safeFilteredUsers.map((user) => (
                <tr key={user.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <img
                        src={user.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(user.fullName)}&background=f97316&color=fff`}
                        alt={user.fullName}
                        className="h-10 w-10 rounded-full object-cover"
                      />
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{user.fullName}</div>
                        <div className="text-sm text-gray-500">{user.email}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getUserTypeColor(user.userType)}`}>
                      {getUserTypeLabel(user.userType)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div>{user.phone || 'N/A'}</div>
                      {user.branch && <div className="text-xs text-gray-500">{user.branch}</div>}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex flex-col space-y-1">
                      {/* Active/Inactive Status */}
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        user.isActive
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {user.isActive ? 'Active' : 'Inactive'}
                      </span>

                      {/* Verified Status (if available) */}
                      {user.isVerified !== undefined && (
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          user.isVerified
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {user.isVerified ? 'Verified' : 'Unverified'}
                        </span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {user.lastLogin ? (
                      <div>
                        <div>{new Date(user.lastLogin).toLocaleDateString()}</div>
                        <div className="text-xs text-gray-400">
                          {new Date(user.lastLogin).toLocaleTimeString()}
                        </div>
                      </div>
                    ) : (
                      'Never'
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-2">
                      {/* Edit Button */}
                      <button
                        onClick={() => {
                          setSelectedUser(user);
                          setShowUserForm(true);
                        }}
                        className="text-orange-600 hover:text-orange-900"
                        title="Edit user"
                      >
                        <Edit className="h-4 w-4" />
                      </button>

                      {/* Status Management Dropdown */}
                      <div className="relative">
                        <button
                          onClick={() => {
                            console.log('Status dropdown clicked for user:', user.id, 'current dropdown:', showStatusDropdown);
                            setShowStatusDropdown(showStatusDropdown === user.id ? null : user.id);
                          }}
                          className="text-blue-600 hover:text-blue-900"
                          title="Manage status"
                          disabled={statusUpdateLoading === user.id}
                        >
                          {statusUpdateLoading === user.id ? (
                            <div className="animate-spin h-4 w-4 border-2 border-blue-600 border-t-transparent rounded-full"></div>
                          ) : (
                            <MoreVertical className="h-4 w-4" />
                          )}
                        </button>

                        {showStatusDropdown === user.id && (
                          <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                            <div className="py-1">
                              {/* Active/Inactive Toggle */}
                              <button
                                onClick={() => {
                                  console.log('Toggle user active clicked:', { userId: user.id, currentStatus: user.isActive, isCurrentUser: user.id === currentUserId });
                                  if (user.id !== currentUserId) {
                                    toggleUserActive(user.id, user.isActive);
                                  }
                                }}
                                className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 flex items-center ${
                                  user.id === currentUserId ? 'opacity-50 cursor-not-allowed' : ''
                                }`}
                                disabled={user.id === currentUserId}
                              >
                                {user.isActive ? (
                                  <>
                                    <Ban className="h-4 w-4 mr-2 text-red-500" />
                                    Deactivate User
                                  </>
                                ) : (
                                  <>
                                    <Check className="h-4 w-4 mr-2 text-green-500" />
                                    Activate User
                                  </>
                                )}
                              </button>

                              {/* Verified Toggle (if verification system exists) */}
                              {user.isVerified !== undefined && (
                                <button
                                  onClick={() => toggleUserVerified(user.id, user.isVerified)}
                                  className="w-full text-left px-4 py-2 text-sm hover:bg-gray-100 flex items-center"
                                >
                                  {user.isVerified ? (
                                    <>
                                      <X className="h-4 w-4 mr-2 text-yellow-500" />
                                      Mark Unverified
                                    </>
                                  ) : (
                                    <>
                                      <CheckCircle className="h-4 w-4 mr-2 text-blue-500" />
                                      Mark Verified
                                    </>
                                  )}
                                </button>
                              )}

                              {/* Status Options */}
                              <div className="border-t border-gray-100 mt-1 pt-1">
                                <div className="px-4 py-2 text-xs font-medium text-gray-500 uppercase">Set Status</div>
                                <button
                                  onClick={() => setUserStatus(user.id, 'active')}
                                  className="w-full text-left px-4 py-2 text-sm hover:bg-gray-100 flex items-center"
                                >
                                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                                  Active
                                </button>
                                <button
                                  onClick={() => setUserStatus(user.id, 'inactive')}
                                  className="w-full text-left px-4 py-2 text-sm hover:bg-gray-100 flex items-center"
                                >
                                  <div className="w-2 h-2 bg-gray-500 rounded-full mr-2"></div>
                                  Inactive
                                </button>
                                <button
                                  onClick={() => setUserStatus(user.id, 'suspended')}
                                  className="w-full text-left px-4 py-2 text-sm hover:bg-gray-100 flex items-center"
                                >
                                  <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                                  Suspended
                                </button>
                                <button
                                  onClick={() => setUserStatus(user.id, 'pending')}
                                  className="w-full text-left px-4 py-2 text-sm hover:bg-gray-100 flex items-center"
                                >
                                  <div className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></div>
                                  Pending
                                </button>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Delete Button */}
                      <button
                        onClick={() => setShowDeleteConfirm(user.id)}
                        className="text-red-600 hover:text-red-900"
                        title="Delete user"
                        disabled={user.id === currentUserId}
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* User Form Modal */}
      {showUserForm && (
        <UserForm
          user={selectedUser}
          onSubmit={selectedUser ? handleUpdateUser : handleCreateUser}
          onCancel={() => {
            setShowUserForm(false);
            setSelectedUser(null);
          }}
        />
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Delete User</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete this user? This action cannot be undone.
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => handleDeleteUser(showDeleteConfirm)}
                className="flex-1 bg-red-500 text-white py-2 px-4 rounded-lg hover:bg-red-600 transition-colors"
              >
                Delete
              </button>
              <button
                onClick={() => setShowDeleteConfirm(null)}
                className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400 transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Enhanced User Form Component with conditional fields
const UserForm = ({ user, onSubmit, onCancel }: any) => {
  const [formData, setFormData] = useState({
    email: user?.email || '',
    fullName: user?.fullName || '',
    userType: user?.userType || 'manager',
    password: '',
    phone: user?.phone || '',
    city: user?.city || 'Tetouan',
    // Company fields (for clients/resellers)
    companyName: user?.companyName || '',
    companyAddress: user?.companyAddress || '',
    businessLicense: user?.businessLicense || '',
    // Delivery fields (for delivery personnel)
    vehicleType: user?.vehicleType || '',
    licenseNumber: user?.licenseNumber || '',
    coverageArea: user?.coverageArea || '',
    // Profile picture
    profilePicture: null as File | null,
    permissions: user?.permissions || []
  });

  const [showPassword, setShowPassword] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!user && !formData.password) {
      alert('Password is required for new users');
      return;
    }
    onSubmit(formData);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setFormData(prev => ({ ...prev, profilePicture: file }));
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          {user ? 'Edit User' : 'Add User'}
        </h3>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="border-b border-gray-200 pb-4">
            <h4 className="text-md font-medium text-gray-900 mb-3">Basic Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Full Name <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.fullName}
                  onChange={(e) => setFormData(prev => ({ ...prev, fullName: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email <span className="text-red-500">*</span>
                </label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  User Type <span className="text-red-500">*</span>
                </label>
                <select
                  value={formData.userType}
                  onChange={(e) => setFormData(prev => ({ ...prev, userType: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  required
                >
                  <option value="admin">Admin</option>
                  <option value="manager">Manager</option>
                  <option value="client">Client</option>
                  <option value="reseller">Reseller</option>
                  <option value="delivery_person">Delivery Personnel</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                <input
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  placeholder="+212 6XX XXX XXX"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">City</label>
                <select
                  value={formData.city}
                  onChange={(e) => setFormData(prev => ({ ...prev, city: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                >
                  {MOROCCAN_CITIES.map(city => (
                    <option key={city} value={city}>{city}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Password Section */}
          <div className="border-b border-gray-200 pb-4">
            <h4 className="text-md font-medium text-gray-900 mb-3">Security</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {user ? 'New Password (leave blank to keep current)' : 'Password'}
                  {!user && <span className="text-red-500">*</span>}
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                    className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    required={!user}
                    minLength={8}
                    placeholder={user ? "Leave blank to keep current password" : "Minimum 8 characters"}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-gray-400" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-400" />
                    )}
                  </button>
                </div>
                {user && (
                  <p className="text-xs text-gray-500 mt-1">
                    Only enter a new password if you want to change it
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Profile Picture</label>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  />
                  <p className="text-xs text-gray-500 mt-1">Upload a profile picture (optional)</p>
                </div>
              </div>
            </div>
          )}

          {/* Conditional Fields for Clients and Resellers */}
          {(formData.userType === 'client' || formData.userType === 'reseller') && (
            <div className="border-b border-gray-200 pb-4">
              <h4 className="text-md font-medium text-gray-900 mb-3">Company Information</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Company Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.companyName}
                    onChange={(e) => setFormData(prev => ({ ...prev, companyName: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Business License</label>
                  <input
                    type="text"
                    value={formData.businessLicense}
                    onChange={(e) => setFormData(prev => ({ ...prev, businessLicense: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    placeholder="License number or registration ID"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Company Address</label>
                  <textarea
                    value={formData.companyAddress}
                    onChange={(e) => setFormData(prev => ({ ...prev, companyAddress: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    rows={3}
                    placeholder="Enter complete company address"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Conditional Fields for Delivery Personnel */}
          {formData.userType === 'delivery_person' && (
            <div className="border-b border-gray-200 pb-4">
              <h4 className="text-md font-medium text-gray-900 mb-3">Delivery Information</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Vehicle Type <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={formData.vehicleType}
                    onChange={(e) => setFormData(prev => ({ ...prev, vehicleType: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    required
                  >
                    <option value="">Select vehicle type</option>
                    <option value="motorcycle">Motorcycle</option>
                    <option value="car">Car</option>
                    <option value="van">Van</option>
                    <option value="truck">Truck</option>
                    <option value="bicycle">Bicycle</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    License Number <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.licenseNumber}
                    onChange={(e) => setFormData(prev => ({ ...prev, licenseNumber: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    placeholder="Driving license number"
                    required
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Coverage Area</label>
                  <textarea
                    value={formData.coverageArea}
                    onChange={(e) => setFormData(prev => ({ ...prev, coverageArea: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    rows={2}
                    placeholder="Areas or regions covered for delivery"
                  />
                </div>
              </div>
            </div>
          )}
          
          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onCancel}
              className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-6 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors flex items-center space-x-2"
            >
              <UserPlus className="h-4 w-4" />
              <span>{user ? 'Update User' : 'Create User'}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default UserManagement;
