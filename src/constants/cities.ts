/**
 * Comprehensive list of Moroccan cities for YalaOffice application
 * Sorted alphabetically for better user experience
 * 
 * This list is used across all components that require city selection:
 * - Sign-up forms
 * - Profile management
 * - User registration
 * - Address forms
 * 
 * Last updated: 2025-01-16
 */

export const MOROCCAN_CITIES = [
  'Ad Dakhla',
  'Ad Darwa',
  'Agadir',
  'A<PERSON>lmous',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  'Ain El Aouda',
  'Ait Melloul',
  'Ait Ourir',
  'Al Aaroui',
  'Al Fqih Ben Çalah',
  'Al Ho<PERSON>ïma',
  '<PERSON>hm<PERSON>',
  "Al 'Attawia",
  '<PERSON><PERSON>oud',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON>mmour',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON><PERSON>',
  'Barr<PERSON><PERSON>',
  '<PERSON>uer<PERSON>',
  '<PERSON><PERSON>',
  '<PERSON><PERSON>',
  'Berkane',
  'Biougra',
  'Bir Jdid',
  'Bou Arfa',
  'Boujad',
  'Bouknadel',
  'Bouskoura',
  '<PERSON><PERSON>i <PERSON>',
  'Casablanca',
  'Chefchaouen',
  'Chichaoua',
  'Demnat',
  'El Aïoun',
  'El Hajeb',
  'El Jadid',
  'El Kelaa des Srarhna',
  'Errachidia',
  'Fnidq',
  'Fès',
  'Guelmim',
  'Guercif',
  'Iheddadene',
  'Imzouren',
  'Inezgane',
  'Jerada',
  'Kenitra',
  'Khénifra',
  'Khouribga',
  'Kouribga',
  'Ksar El Kebir',
  'Laâyoune',
  'Larache',
  'M\'diq',
  'Marrakech',
  'Martil',
  'Mechraa Bel Ksiri',
  'Mehdya',
  'Meknès',
  'Midalt',
  'Missour',
  'Mohammedia',
  'Moulay Abdallah',
  'Moulay Bousselham',
  'Mrirt',
  'My Drarga',
  'Nador',
  'Oued Zem',
  'Oujda-Angad',
  'Oulad Barhil',
  'Oulad Tayeb',
  'Oulad Teïma',
  'Oulad Yaïch',
  'Ouezzane',
  'Qasbat Tadla',
  'Rabat',
  'Safi',
  'Sale',
  'Sefrou',
  'Settat',
  'Sidi Qacem',
  'Sidi Slimane',
  'Sidi Smai\'il',
  'Sidi Yahia El Gharb',
  'Sidi Yahya Zaer',
  'Skhirate',
  'Souk et Tnine Jorf el Mellah',
  'Souq Sebt Oulad Nemma',
  'Tahla',
  'Tameslouht',
  'Tangier',
  'Taourirt',
  'Taza',
  'Temara',
  'Temsia',
  'Tifariti',
  'Tit Mellil',
  'Tiznit',
  'Tétouan',
  'Youssoufia',
  'Zagora',
  'Zawyat ech Cheïkh',
  'Zaïo',
  'Zeghanghane'
] as const;

/**
 * Type definition for Moroccan cities
 */
export type MoroccanCity = typeof MOROCCAN_CITIES[number];

/**
 * Helper function to validate if a city is in the approved list
 */
export const isValidMoroccanCity = (city: string): city is MoroccanCity => {
  return MOROCCAN_CITIES.includes(city as MoroccanCity);
};

/**
 * Helper function to get city options for select components
 */
export const getCityOptions = () => {
  return MOROCCAN_CITIES.map(city => ({
    value: city,
    label: city
  }));
};

/**
 * Major cities for quick access or default selections
 */
export const MAJOR_MOROCCAN_CITIES = [
  'Casablanca',
  'Rabat',
  'Fès',
  'Marrakech',
  'Tangier',
  'Agadir',
  'Meknès',
  'Oujda-Angad',
  'Kenitra',
  'Tétouan',
  'Sale',
  'Safi',
  'Mohammedia',
  'Khouribga',
  'Beni Mellal',
  'El Jadid',
  'Taza',
  'Nador',
  'Settat',
  'Larache'
] as const;
