/**
 * Live Data Service for YalaOffice
 * Replaces all mock data with real Supabase database integration
 */

import { supabase } from '@/integrations/supabase/client';
import type { Database } from '@/integrations/supabase/types';
import { Product, Category } from '../types/inventory';

// Type definitions
type User = Database['public']['Tables']['users']['Row'];
type Product = Database['public']['Tables']['products']['Row'];
type Order = Database['public']['Tables']['orders']['Row'];
type Branch = Database['public']['Tables']['branches']['Row'];
type Category = Database['public']['Tables']['categories']['Row'];
type CustomerProfile = Database['public']['Tables']['customer_profiles']['Row'];

export class LiveDataService {
  private static instance: LiveDataService;

  private constructor() {}

  static getInstance(): LiveDataService {
    if (!LiveDataService.instance) {
      LiveDataService.instance = new LiveDataService();
    }
    return LiveDataService.instance;
  }

  // Helper function to transform user data from database format to frontend format
  private transformUserData(users: any[]): User[] {
    return users.map(user => ({
      ...user,
      userType: user.user_type, // Transform snake_case to camelCase
      fullName: user.full_name,
      isActive: user.is_active,
      isVerified: user.is_verified,
      companyName: user.company_name,
      iceNumber: user.ice_number,
      companyAddress: user.company_address,
      companyPhone: user.company_phone,
      companyCity: user.company_city,
      companyEmail: user.company_email,
      taxId: user.tax_id,
      legalForm: user.legal_form,
      isCompany: user.is_company,
      createdAt: user.created_at,
      updatedAt: user.updated_at,
      lastLogin: user.last_login
    }));
  }

  // =============================================
  // USER MANAGEMENT
  // =============================================

  async getAllUsers(): Promise<User[]> {
    console.log('LiveDataService: Fetching all users...');

    try {
      // First, try to get current user to check if they're admin/manager
      const { data: { user: currentUser } } = await supabase.auth.getUser();
      console.log('Current user:', currentUser?.id);

      // Use RPC function to bypass RLS for admin users
      const { data, error } = await supabase.rpc('get_all_users_admin');

      if (error) {
        console.log('RPC failed, trying direct query:', error);
        // Fallback to direct query
        const { data: fallbackData, error: fallbackError } = await supabase
          .from('users')
          .select('*')
          .order('created_at', { ascending: false });

        console.log('LiveDataService: Fallback query result:', { data: fallbackData, error: fallbackError, dataLength: fallbackData?.length });

        if (fallbackError) {
          console.error('Error fetching users:', fallbackError);

          // Let's also check if there are any users at all with a simple count
          const { count, error: countError } = await supabase
            .from('users')
            .select('*', { count: 'exact', head: true });

          console.log('LiveDataService: Users count check:', { count, countError });

          throw fallbackError;
        }

        return fallbackData || [];
      }

      console.log('LiveDataService: RPC query result:', { data, error, dataLength: data?.length });

      // If RPC returns empty but no error, let's check if there are users in the table
      if (data && data.length === 0) {
        const { count, error: countError } = await supabase
          .from('users')
          .select('*', { count: 'exact', head: true });

        console.log('LiveDataService: Empty RPC result, checking users count:', { count, countError });
      }

      // Transform database format to frontend format
      const transformedData = this.transformUserData(data || []);
      console.log('LiveDataService: Transformed user data:', transformedData);
      return transformedData;
    } catch (err) {
      console.error('Error in getAllUsers:', err);
      throw err;
    }
  }

  async ensureCurrentUserInUsersTable(): Promise<void> {
    try {
      const { data: { user: currentAuthUser } } = await supabase.auth.getUser();

      if (!currentAuthUser) {
        console.log('No authenticated user found');
        return;
      }

      console.log('Checking if current user exists in users table:', currentAuthUser.id);

      // Use a more direct approach to avoid RLS issues
      try {
        // First try to get the user using RPC function
        const { data: allUsers, error: rpcError } = await supabase.rpc('get_all_users_admin');

        if (!rpcError && allUsers) {
          const existingUser = allUsers.find((user: any) => user.id === currentAuthUser.id);

          if (existingUser) {
            console.log('User found via RPC:', existingUser);
            return;
          }
        }
      } catch (rpcErr) {
        console.log('RPC approach failed, trying direct query:', rpcErr);
      }

      // If RPC fails or user not found, try direct query with temporary RLS bypass
      console.log('User not found, attempting to create record...');

      // Extract user metadata
      const metadata = currentAuthUser.user_metadata || {};
      const email = currentAuthUser.email || '';

      // Create user record with proper status fields
      const { data: newUser, error: insertError } = await supabase
        .from('users')
        .insert({
          id: currentAuthUser.id,
          email: email,
          full_name: metadata.full_name || metadata.fullName || email.split('@')[0],
          user_type: metadata.user_type || metadata.userType || 'admin', // Default to admin for now
          phone: metadata.phone || '+212 6 12 34 56 78',
          city: metadata.city || 'Tetouan',
          is_active: true,
          is_verified: true, // Default to verified for admin users
          status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (insertError) {
        console.error('Error creating user record:', insertError);
        // If insert fails due to RLS, the user might already exist
        console.log('Insert failed, user might already exist. This is OK if RLS is blocking the check.');
      } else {
        console.log('User record created successfully:', newUser);
      }
    } catch (err) {
      console.error('Error in ensureCurrentUserInUsersTable:', err);
      // Don't throw the error, just log it
    }
  }

  // New method for admin user status management
  async updateUserStatus(userId: string, updates: { is_active?: boolean; is_verified?: boolean; status?: string }): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('LiveDataService: Updating user status:', { userId, updates });

      const updateData: any = {
        updated_at: new Date().toISOString()
      };

      if (updates.is_active !== undefined) updateData.is_active = updates.is_active;
      if (updates.is_verified !== undefined) updateData.is_verified = updates.is_verified;
      if (updates.status !== undefined) updateData.status = updates.status;

      const { data, error } = await supabase
        .from('users')
        .update(updateData)
        .eq('id', userId)
        .select()
        .single();

      if (error) {
        console.error('Error updating user status:', error);
        return { success: false, error: error.message };
      }

      console.log('LiveDataService: User status updated successfully:', data);
      return { success: true };
    } catch (err) {
      console.error('Error in updateUserStatus:', err);
      return { success: false, error: (err as Error).message };
    }
  }

  async getUsersByType(userType: string): Promise<User[]> {
    try {
      // Use RPC function for admin access
      const { data, error } = await supabase.rpc('get_all_users_admin');

      if (error) {
        console.log('RPC failed for getUsersByType, trying direct query:', error);
        // Fallback to direct query
        const { data: fallbackData, error: fallbackError } = await supabase
          .from('users')
          .select('*')
          .eq('user_type', userType)
          .eq('is_active', true)
          .order('full_name');

        if (fallbackError) {
          console.error('Error fetching users by type:', fallbackError);
          throw fallbackError;
        }

        const filteredData = (fallbackData || []).filter(user => user.user_type === userType && user.is_active);
        return this.transformUserData(filteredData);
      }

      // Filter the results by type and active status
      const filteredData = (data || []).filter(user => user.user_type === userType && user.is_active);
      return this.transformUserData(filteredData);
    } catch (err) {
      console.error('Error in getUsersByType:', err);
      throw err;
    }
  }

  async getUserById(id: string): Promise<User | null> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching user by ID:', error);
      return null;
    }

    return data;
  }

  async getUserStatistics(): Promise<any> {
    try {
      console.log('LiveDataService: Fetching user statistics...');

      // First, let's check if current user exists in users table
      const { data: { user: currentAuthUser } } = await supabase.auth.getUser();
      console.log('LiveDataService: Current auth user:', currentAuthUser?.id);

      if (currentAuthUser) {
        const { data: currentUserRecord, error: currentUserError } = await supabase
          .from('users')
          .select('*')
          .eq('id', currentAuthUser.id)
          .single();

        console.log('LiveDataService: Current user in users table:', {
          found: !!currentUserRecord,
          user: currentUserRecord,
          error: currentUserError
        });
      }

      const { data, error } = await supabase.rpc('get_user_statistics');

      if (error) {
        console.log('RPC failed for getUserStatistics, calculating manually:', error);

        // Fallback: calculate statistics manually
        const { data: users, error: usersError } = await supabase
          .from('users')
          .select('user_type, is_active, created_at');

        console.log('LiveDataService: Direct users query result:', { users, usersError, count: users?.length });

        if (usersError) {
          console.error('Error fetching users for statistics:', usersError);
          throw usersError;
        }

        const stats = {
          total: users?.length || 0,
          admins: users?.filter(u => u.user_type === 'admin').length || 0,
          managers: users?.filter(u => u.user_type === 'manager').length || 0,
          clients: users?.filter(u => u.user_type === 'client').length || 0,
          resellers: users?.filter(u => u.user_type === 'reseller').length || 0,
          delivery: users?.filter(u => u.user_type === 'delivery_person').length || 0,
          active: users?.filter(u => u.is_active).length || 0,
          inactive: users?.filter(u => !u.is_active).length || 0,
          recentlyCreated: users?.filter(u => {
            const weekAgo = new Date();
            weekAgo.setDate(weekAgo.getDate() - 7);
            return new Date(u.created_at) >= weekAgo;
          }).length || 0
        };

        console.log('LiveDataService: Manual statistics result:', stats);
        return stats;
      }

      console.log('LiveDataService: RPC statistics result:', data);
      return data;
    } catch (err) {
      console.error('Error in getUserStatistics:', err);
      throw err;
    }
  }

  async createUser(userData: Partial<User>): Promise<User | null> {
    const { data, error } = await supabase
      .from('users')
      .insert([userData])
      .select()
      .single();

    if (error) {
      console.error('Error creating user:', error);
      throw error;
    }

    return data;
  }

  async updateUser(id: string, updates: Partial<User>): Promise<User | null> {
    const { data, error } = await supabase
      .from('users')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating user:', error);
      throw error;
    }

    return data;
  }

  async deleteUser(id: string): Promise<boolean> {
    const { error } = await supabase
      .from('users')
      .update({ is_active: false, updated_at: new Date().toISOString() })
      .eq('id', id);

    if (error) {
      console.error('Error deactivating user:', error);
      return false;
    }

    return true;
  }

  // =============================================
  // PRODUCT MANAGEMENT
  // =============================================

  async getAllProducts(): Promise<Product[]> {
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        categories (
          id,
          name,
          color,
          icon
        )
      `)
      .eq('is_active', true)
      .order('title');

    if (error) {
      console.error('Error fetching products:', error);
      throw error;
    }

    // Transform raw Supabase data to Product objects
    return (data || []).map(productRow => this.convertToProduct(productRow));
  }

  // Helper function to convert database rows to Product objects
  private convertToProduct(productRow: any): Product {
    return {
      id: productRow.id,
      title: productRow.title,
      description: productRow.description || '',
      category: productRow.categories?.name || 'Uncategorized',
      brand: productRow.brand || '',
      price: Number(productRow.price),
      resellerPrice: Number(productRow.reseller_price || productRow.price),
      image: productRow.featured_image || '/placeholder.svg',
      featuredImage: productRow.featured_image || '/placeholder.svg',
      thumbnailImages: productRow.thumbnail_images || ['/placeholder.svg'],
      rating: Number(productRow.rating || 0),
      stock: productRow.stock || 0,
      minStock: productRow.min_stock || 0,
      isActive: productRow.is_active,
      isNew: productRow.is_new || false,
      sku: productRow.sku,
      weight: productRow.weight ? Number(productRow.weight) : undefined,
      dimensions: productRow.dimensions as Product['dimensions'],
      tags: productRow.tags || [],
      createdAt: productRow.created_at,
      updatedAt: productRow.updated_at,
    };
  }

  async getProductsByCategory(categoryId: string): Promise<Product[]> {
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        categories (
          id,
          name,
          color,
          icon
        )
      `)
      .eq('category_id', categoryId)
      .eq('is_active', true)
      .order('title');

    if (error) {
      console.error('Error fetching products by category:', error);
      throw error;
    }

    // Transform raw Supabase data to Product objects
    return (data || []).map(productRow => this.convertToProduct(productRow));
  }

  async getProductById(id: string): Promise<Product | null> {
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        categories (
          id,
          name,
          color,
          icon
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching product by ID:', error);
      return null;
    }

    return data ? this.convertToProduct(data) : null;
  }

  async searchProducts(query: string): Promise<Product[]> {
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        categories (
          id,
          name,
          color,
          icon
        )
      `)
      .or(`title.ilike.%${query}%,description.ilike.%${query}%,sku.ilike.%${query}%`)
      .eq('is_active', true)
      .order('title')
      .limit(50);

    if (error) {
      console.error('Error searching products:', error);
      throw error;
    }

    // Transform raw Supabase data to Product objects
    return (data || []).map(productRow => this.convertToProduct(productRow));
  }

  async createProduct(productData: Partial<Product>): Promise<Product | null> {
    const { data, error } = await supabase
      .from('products')
      .insert([productData])
      .select(`
        *,
        categories (
          id,
          name,
          color,
          icon
        )
      `)
      .single();

    if (error) {
      console.error('Error creating product:', error);
      throw error;
    }

    return data ? this.convertToProduct(data) : null;
  }

  async updateProduct(id: string, updates: Partial<Product>): Promise<Product | null> {
    const { data, error } = await supabase
      .from('products')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select(`
        *,
        categories (
          id,
          name,
          color,
          icon
        )
      `)
      .single();

    if (error) {
      console.error('Error updating product:', error);
      throw error;
    }

    return data ? this.convertToProduct(data) : null;
  }

  async updateProductStock(id: string, newStock: number): Promise<boolean> {
    const { error } = await supabase
      .from('products')
      .update({ 
        stock: newStock, 
        updated_at: new Date().toISOString() 
      })
      .eq('id', id);

    if (error) {
      console.error('Error updating product stock:', error);
      return false;
    }

    return true;
  }

  // =============================================
  // CATEGORY MANAGEMENT
  // =============================================

  async getAllCategories(): Promise<Category[]> {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('is_active', true)
      .order('sort_order');

    if (error) {
      console.error('Error fetching categories:', error);
      throw error;
    }

    return data || [];
  }

  async getCategoryById(id: string): Promise<Category | null> {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching category by ID:', error);
      return null;
    }

    return data;
  }

  // =============================================
  // BRANCH MANAGEMENT
  // =============================================

  async getAllBranches(): Promise<Branch[]> {
    const { data, error } = await supabase
      .from('branches')
      .select('*')
      .eq('is_active', true)
      .order('name');

    if (error) {
      console.error('Error fetching branches:', error);
      throw error;
    }

    return data || [];
  }

  async getBranchById(id: string): Promise<Branch | null> {
    const { data, error } = await supabase
      .from('branches')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching branch by ID:', error);
      return null;
    }

    return data;
  }

  async getMainBranch(): Promise<Branch | null> {
    const { data, error } = await supabase
      .from('branches')
      .select('*')
      .eq('is_main_branch', true)
      .eq('is_active', true)
      .single();

    if (error) {
      console.error('Error fetching main branch:', error);
      return null;
    }

    return data;
  }

  // =============================================
  // ORDER MANAGEMENT
  // =============================================

  async getAllOrders(): Promise<Order[]> {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        users!orders_customer_id_fkey (
          id,
          full_name,
          email,
          phone
        ),
        order_items (
          id,
          quantity,
          unit_price,
          total_price,
          products (
            id,
            title,
            sku,
            featured_image
          )
        )
      `)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching orders:', error);
      throw error;
    }

    return data || [];
  }

  async getOrdersByStatus(status: string): Promise<Order[]> {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        users!orders_customer_id_fkey (
          id,
          full_name,
          email,
          phone
        ),
        order_items (
          id,
          quantity,
          unit_price,
          total_price,
          products (
            id,
            title,
            sku,
            featured_image
          )
        )
      `)
      .eq('status', status)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching orders by status:', error);
      throw error;
    }

    return data || [];
  }

  async getOrderById(id: string): Promise<Order | null> {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        users!orders_customer_id_fkey (
          id,
          full_name,
          email,
          phone,
          city
        ),
        order_items (
          id,
          quantity,
          unit_price,
          total_price,
          products (
            id,
            title,
            sku,
            featured_image,
            brand
          )
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching order by ID:', error);
      return null;
    }

    return data;
  }

  // =============================================
  // CUSTOMER MANAGEMENT
  // =============================================

  async getAllCustomers(): Promise<(User & { customer_profiles?: CustomerProfile })[]> {
    const { data, error } = await supabase
      .from('users')
      .select(`
        *,
        customer_profiles (
          id,
          discount_rate,
          credit_limit,
          total_orders,
          total_spent,
          last_order_date,
          loyalty_points,
          status
        )
      `)
      .in('user_type', ['client', 'reseller'])
      .eq('is_active', true)
      .order('full_name');

    if (error) {
      console.error('Error fetching customers:', error);
      throw error;
    }

    return data || [];
  }

  async getCustomerById(id: string): Promise<(User & { customer_profiles?: CustomerProfile }) | null> {
    const { data, error } = await supabase
      .from('users')
      .select(`
        *,
        customer_profiles (
          id,
          discount_rate,
          credit_limit,
          total_orders,
          total_spent,
          last_order_date,
          loyalty_points,
          status
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching customer by ID:', error);
      return null;
    }

    return data;
  }

  // =============================================
  // ANALYTICS & REPORTING
  // =============================================

  async getDashboardStats(): Promise<{
    totalProducts: number;
    totalOrders: number;
    totalCustomers: number;
    totalRevenue: number;
    lowStockProducts: number;
    pendingOrders: number;
  }> {
    try {
      const [
        productsCount,
        ordersCount,
        customersCount,
        revenueSum,
        lowStockCount,
        pendingOrdersCount
      ] = await Promise.all([
        supabase.from('products').select('id', { count: 'exact' }).eq('is_active', true),
        supabase.from('orders').select('id', { count: 'exact' }),
        supabase.from('users').select('id', { count: 'exact' }).in('user_type', ['client', 'reseller']).eq('is_active', true),
        supabase.from('orders').select('total').eq('status', 'completed'),
        supabase.from('products').select('id', { count: 'exact' }).lt('stock', 'min_stock').eq('is_active', true),
        supabase.from('orders').select('id', { count: 'exact' }).eq('status', 'pending')
      ]);

      const totalRevenue = revenueSum.data?.reduce((sum, order) => sum + (order.total || 0), 0) || 0;

      return {
        totalProducts: productsCount.count || 0,
        totalOrders: ordersCount.count || 0,
        totalCustomers: customersCount.count || 0,
        totalRevenue,
        lowStockProducts: lowStockCount.count || 0,
        pendingOrders: pendingOrdersCount.count || 0
      };
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      return {
        totalProducts: 0,
        totalOrders: 0,
        totalCustomers: 0,
        totalRevenue: 0,
        lowStockProducts: 0,
        pendingOrders: 0
      };
    }
  }

  // =============================================
  // REAL-TIME SUBSCRIPTIONS
  // =============================================

  subscribeToProducts(callback: (payload: any) => void) {
    return supabase
      .channel('products-changes')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'products' }, 
        callback
      )
      .subscribe();
  }

  subscribeToOrders(callback: (payload: any) => void) {
    return supabase
      .channel('orders-changes')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'orders' }, 
        callback
      )
      .subscribe();
  }

  subscribeToUsers(callback: (payload: any) => void) {
    return supabase
      .channel('users-changes')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'users' }, 
        callback
      )
      .subscribe();
  }
}

// Export singleton instance
export const liveDataService = LiveDataService.getInstance();
export default liveDataService;
