/**
 * Live Category Service for YalaOffice
 * Uses real Supabase data instead of mock data
 */

import { Category } from '../types/management';
import { supabase } from '@/integrations/supabase/client';
import type { Database } from '@/integrations/supabase/types';
import { realTimeService } from './realTimeService';

// Type definitions for database integration
type CategoryRow = Database['public']['Tables']['categories']['Row'];
type CategoryInsert = Database['public']['Tables']['categories']['Insert'];
type CategoryUpdate = Database['public']['Tables']['categories']['Update'];

// Helper function to convert database row to Category type
const convertToCategory = (categoryRow: CategoryRow, productCount: number = 0): Category => {
  return {
    id: categoryRow.id,
    name: categoryRow.name,
    description: categoryRow.description || '',
    level: categoryRow.level || 0,
    parentId: categoryRow.parent_id || undefined,
    isActive: categoryRow.is_active,
    sortOrder: categoryRow.sort_order || 0,
    icon: categoryRow.icon || 'folder',
    color: categoryRow.color || '#6B7280',
    productCount,
    createdAt: categoryRow.created_at,
    updatedAt: categoryRow.updated_at,
    createdBy: categoryRow.created_by || 'system',
    updatedBy: categoryRow.updated_by || 'system'
  };
};

// Get product count for a category
const getProductCount = async (categoryId: string): Promise<number> => {
  try {
    const { count, error } = await supabase
      .from('products')
      .select('*', { count: 'exact', head: true })
      .eq('category_id', categoryId)
      .eq('is_active', true);

    if (error) {
      console.error('Error getting product count:', error);
      return 0;
    }

    return count || 0;
  } catch (error) {
    console.error('Error in getProductCount:', error);
    return 0;
  }
};

// Category service functions using live data
export const getCategories = async (): Promise<Category[]> => {
  try {
    const { data: categories, error } = await supabase
      .from('categories')
      .select('*')
      .eq('is_active', true)
      .order('sort_order');

    if (error) {
      console.error('Error fetching categories:', error);
      throw error;
    }

    // Get product counts for each category
    const categoriesWithCounts = await Promise.all(
      (categories || []).map(async (category) => {
        const productCount = await getProductCount(category.id);
        return convertToCategory(category, productCount);
      })
    );

    return categoriesWithCounts;
  } catch (error) {
    console.error('Error in getCategories:', error);
    return [];
  }
};

export const getCategoryById = async (id: string): Promise<Category | null> => {
  try {
    const { data: category, error } = await supabase
      .from('categories')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching category:', error);
      return null;
    }

    if (!category) return null;

    const productCount = await getProductCount(category.id);
    return convertToCategory(category, productCount);
  } catch (error) {
    console.error('Error in getCategoryById:', error);
    return null;
  }
};

export const getCategoriesByParent = async (parentId?: string): Promise<Category[]> => {
  try {
    let query = supabase
      .from('categories')
      .select('*')
      .eq('is_active', true)
      .order('sort_order');

    if (parentId) {
      query = query.eq('parent_id', parentId);
    } else {
      query = query.is('parent_id', null);
    }

    const { data: categories, error } = await query;

    if (error) {
      console.error('Error fetching categories by parent:', error);
      throw error;
    }

    // Get product counts for each category
    const categoriesWithCounts = await Promise.all(
      (categories || []).map(async (category) => {
        const productCount = await getProductCount(category.id);
        return convertToCategory(category, productCount);
      })
    );

    return categoriesWithCounts;
  } catch (error) {
    console.error('Error in getCategoriesByParent:', error);
    return [];
  }
};

export const getCategoryHierarchy = async (): Promise<Category[]> => {
  try {
    const allCategories = await getCategories();
    
    const buildHierarchy = (parentId?: string): Category[] => {
      return allCategories
        .filter(cat => cat.parentId === parentId)
        .sort((a, b) => a.sortOrder - b.sortOrder)
        .map(cat => ({
          ...cat,
          children: buildHierarchy(cat.id)
        }));
    };
    
    return buildHierarchy();
  } catch (error) {
    console.error('Error in getCategoryHierarchy:', error);
    return [];
  }
};

export const createCategory = async (
  categoryData: Omit<Category, 'id' | 'createdAt' | 'updatedAt' | 'productCount'>, 
  userId: string
): Promise<Category> => {
  try {
    const categoryInsert: CategoryInsert = {
      name: categoryData.name,
      description: categoryData.description,
      level: categoryData.level,
      parent_id: categoryData.parentId,
      is_active: categoryData.isActive,
      sort_order: categoryData.sortOrder,
      icon: categoryData.icon,
      color: categoryData.color,
      created_by: userId,
      updated_by: userId
    };

    const { data: newCategory, error } = await supabase
      .from('categories')
      .insert(categoryInsert)
      .select()
      .single();

    if (error) {
      console.error('Error creating category:', error);
      throw error;
    }

    const category = convertToCategory(newCategory, 0);
    
    // Emit real-time event
    realTimeService.emit('category-created', {
      category,
      userId
    });
    
    console.log('Category created:', category);
    return category;
  } catch (error) {
    console.error('Error in createCategory:', error);
    throw error;
  }
};

export const updateCategory = async (
  id: string, 
  updates: Partial<Category>, 
  userId: string
): Promise<Category | null> => {
  try {
    // Get old category for event
    const oldCategory = await getCategoryById(id);
    if (!oldCategory) return null;

    const categoryUpdate: CategoryUpdate = {
      name: updates.name,
      description: updates.description,
      level: updates.level,
      parent_id: updates.parentId,
      is_active: updates.isActive,
      sort_order: updates.sortOrder,
      icon: updates.icon,
      color: updates.color,
      updated_by: userId
    };

    const { data: updatedCategory, error } = await supabase
      .from('categories')
      .update(categoryUpdate)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating category:', error);
      throw error;
    }

    const productCount = await getProductCount(id);
    const category = convertToCategory(updatedCategory, productCount);
    
    // Emit real-time event
    realTimeService.emit('category-updated', {
      categoryId: id,
      oldData: oldCategory,
      newData: category,
      userId
    });
    
    console.log('Category updated:', category);
    return category;
  } catch (error) {
    console.error('Error in updateCategory:', error);
    return null;
  }
};

export const deleteCategory = async (id: string, userId: string): Promise<boolean> => {
  try {
    const category = await getCategoryById(id);
    if (!category) return false;
    
    // Check if category has children
    const children = await getCategoriesByParent(id);
    if (children.length > 0) {
      throw new Error('Cannot delete category with subcategories. Please delete or move subcategories first.');
    }
    
    // Check if category has products
    if (category.productCount > 0) {
      throw new Error('Cannot delete category with products. Please move or delete products first.');
    }

    const { error } = await supabase
      .from('categories')
      .update({ is_active: false, updated_by: userId })
      .eq('id', id);

    if (error) {
      console.error('Error deleting category:', error);
      throw error;
    }
    
    // Emit real-time event
    realTimeService.emit('category-deleted', {
      categoryId: id,
      category,
      userId
    });
    
    console.log('Category deleted:', id);
    return true;
  } catch (error) {
    console.error('Error in deleteCategory:', error);
    throw error;
  }
};

export const searchCategories = async (query: string): Promise<Category[]> => {
  try {
    const { data: categories, error } = await supabase
      .from('categories')
      .select('*')
      .or(`name.ilike.%${query}%,description.ilike.%${query}%`)
      .eq('is_active', true)
      .order('sort_order');

    if (error) {
      console.error('Error searching categories:', error);
      throw error;
    }

    // Get product counts for each category
    const categoriesWithCounts = await Promise.all(
      (categories || []).map(async (category) => {
        const productCount = await getProductCount(category.id);
        return convertToCategory(category, productCount);
      })
    );

    return categoriesWithCounts;
  } catch (error) {
    console.error('Error in searchCategories:', error);
    return [];
  }
};

export const getCategoryStats = async (): Promise<{
  total: number;
  active: number;
  inactive: number;
  withProducts: number;
  empty: number;
}> => {
  try {
    const [
      { count: total },
      { count: active },
      { count: inactive }
    ] = await Promise.all([
      supabase.from('categories').select('*', { count: 'exact', head: true }),
      supabase.from('categories').select('*', { count: 'exact', head: true }).eq('is_active', true),
      supabase.from('categories').select('*', { count: 'exact', head: true }).eq('is_active', false)
    ]);

    const allCategories = await getCategories();
    const withProducts = allCategories.filter(cat => cat.productCount > 0).length;
    const empty = allCategories.filter(cat => cat.productCount === 0).length;

    return {
      total: total || 0,
      active: active || 0,
      inactive: inactive || 0,
      withProducts,
      empty
    };
  } catch (error) {
    console.error('Error in getCategoryStats:', error);
    return {
      total: 0,
      active: 0,
      inactive: 0,
      withProducts: 0,
      empty: 0
    };
  }
};

export default {
  getCategories,
  getCategoryById,
  getCategoriesByParent,
  getCategoryHierarchy,
  createCategory,
  updateCategory,
  deleteCategory,
  searchCategories,
  getCategoryStats
};
