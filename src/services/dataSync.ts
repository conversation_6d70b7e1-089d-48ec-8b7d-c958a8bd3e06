/**
 * Centralized Data Synchronization Service
 * Ensures all CRUD operations are synchronized across the system and database
 */

import { supabase } from '@/integrations/supabase/client';
import { liveDataService } from './liveDataService';
import { liveDashboardService } from './liveDashboardService';

export interface SyncResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

export class DataSyncService {
  private static instance: DataSyncService;
  private subscribers: Map<string, ((data: any) => void)[]> = new Map();

  private constructor() {}

  static getInstance(): DataSyncService {
    if (!DataSyncService.instance) {
      DataSyncService.instance = new DataSyncService();
    }
    return DataSyncService.instance;
  }

  /**
   * Subscribe to data changes for real-time updates
   */
  subscribe(table: string, callback: (data: any) => void): () => void {
    if (!this.subscribers.has(table)) {
      this.subscribers.set(table, []);
    }
    this.subscribers.get(table)!.push(callback);

    // Return unsubscribe function
    return () => {
      const callbacks = this.subscribers.get(table);
      if (callbacks) {
        const index = callbacks.indexOf(callback);
        if (index > -1) {
          callbacks.splice(index, 1);
        }
      }
    };
  }

  /**
   * Notify all subscribers of data changes
   */
  private notifySubscribers(table: string, data: any) {
    try {
      const callbacks = this.subscribers.get(table);
      if (callbacks && callbacks.length > 0) {
        callbacks.forEach(callback => {
          try {
            callback(data);
          } catch (error) {
            console.error(`Error in subscriber callback for ${table}:`, error);
          }
        });
      }
    } catch (error) {
      console.error(`Error notifying subscribers for ${table}:`, error);
    }
  }

  // ==================== USER OPERATIONS ====================

  /**
   * Create a new user with full synchronization
   */
  async createUser(userData: {
    email: string;
    password: string;
    full_name: string;
    user_type: string;
    phone?: string;
    city?: string;
    company_name?: string;
    company_address?: string;
  }): Promise<SyncResult> {
    try {
      // 1. Create user in Supabase Auth
      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email: userData.email,
        password: userData.password,
        email_confirm: true,
        user_metadata: {
          full_name: userData.full_name,
          user_type: userData.user_type,
          phone: userData.phone,
          city: userData.city
        }
      });

      if (authError) throw authError;

      // 2. Create user profile in database
      const { data: profileData, error: profileError } = await supabase
        .from('users')
        .insert({
          id: authData.user.id,
          email: userData.email,
          full_name: userData.full_name,
          user_type: userData.user_type,
          phone: userData.phone,
          city: userData.city,
          company_name: userData.company_name,
          company_address: userData.company_address,
          is_active: true
        })
        .select()
        .single();

      if (profileError) throw profileError;

      // 3. Create customer profile if needed
      if (['client', 'reseller'].includes(userData.user_type)) {
        await supabase.from('customer_profiles').insert({
          user_id: authData.user.id,
          customer_type: userData.user_type,
          credit_limit: userData.user_type === 'reseller' ? 10000 : 5000,
          discount_percentage: userData.user_type === 'reseller' ? 15 : 5,
          loyalty_points: 0
        });
      }

      // 4. Notify subscribers
      this.notifySubscribers('users', { action: 'create', data: profileData });

      return { success: true, data: profileData };
    } catch (error: any) {
      console.error('Error creating user:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Update user with full synchronization
   */
  async updateUser(userId: string, updates: any): Promise<SyncResult> {
    try {
      // 1. Update user profile in database
      const { data, error } = await supabase
        .from('users')
        .update(updates)
        .eq('id', userId)
        .select()
        .single();

      if (error) throw error;

      // 2. Update auth metadata if needed
      if (updates.full_name || updates.user_type || updates.phone) {
        await supabase.auth.admin.updateUserById(userId, {
          user_metadata: {
            full_name: updates.full_name,
            user_type: updates.user_type,
            phone: updates.phone
          }
        });
      }

      // 3. Notify subscribers
      this.notifySubscribers('users', { action: 'update', data });

      return { success: true, data };
    } catch (error: any) {
      console.error('Error updating user:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Delete user with full synchronization
   */
  async deleteUser(userId: string): Promise<SyncResult> {
    try {
      // 1. Soft delete in database
      const { data, error } = await supabase
        .from('users')
        .update({ is_active: false })
        .eq('id', userId)
        .select()
        .single();

      if (error) throw error;

      // 2. Delete from auth (optional - or just disable)
      // await supabase.auth.admin.deleteUser(userId);

      // 3. Notify subscribers
      this.notifySubscribers('users', { action: 'delete', data });

      return { success: true, data };
    } catch (error: any) {
      console.error('Error deleting user:', error);
      return { success: false, error: error.message };
    }
  }

  // ==================== PRODUCT OPERATIONS ====================

  /**
   * Create product with full synchronization
   */
  async createProduct(productData: {
    title: string;
    description?: string;
    price: number;
    stock: number;
    min_stock: number;
    category_id: string;
    featured_image?: string;
    sku?: string;
  }): Promise<SyncResult> {
    try {
      const { data, error } = await supabase
        .from('products')
        .insert({
          ...productData,
          is_active: true,
          created_at: new Date().toISOString()
        })
        .select(`
          *,
          categories (
            id,
            name,
            color
          )
        `)
        .single();

      if (error) throw error;

      // Notify subscribers
      this.notifySubscribers('products', { action: 'create', data });

      return { success: true, data };
    } catch (error: any) {
      console.error('Error creating product:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Update product with full synchronization
   */
  async updateProduct(productId: string, updates: any): Promise<SyncResult> {
    try {
      const { data, error } = await supabase
        .from('products')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', productId)
        .select(`
          *,
          categories (
            id,
            name,
            color
          )
        `)
        .single();

      if (error) throw error;

      // Notify subscribers
      this.notifySubscribers('products', { action: 'update', data });

      return { success: true, data };
    } catch (error: any) {
      console.error('Error updating product:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Delete product with full synchronization
   */
  async deleteProduct(productId: string): Promise<SyncResult> {
    try {
      const { data, error } = await supabase
        .from('products')
        .update({ is_active: false })
        .eq('id', productId)
        .select()
        .single();

      if (error) throw error;

      // Notify subscribers
      this.notifySubscribers('products', { action: 'delete', data });

      return { success: true, data };
    } catch (error: any) {
      console.error('Error deleting product:', error);
      return { success: false, error: error.message };
    }
  }

  // ==================== CATEGORY OPERATIONS ====================

  /**
   * Create category with full synchronization
   */
  async createCategory(categoryData: {
    name: string;
    description?: string;
    color: string;
    icon?: string;
  }): Promise<SyncResult> {
    try {
      const { data, error } = await supabase
        .from('categories')
        .insert({
          ...categoryData,
          is_active: true,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      // Notify subscribers
      this.notifySubscribers('categories', { action: 'create', data });

      return { success: true, data };
    } catch (error: any) {
      console.error('Error creating category:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Update category with full synchronization
   */
  async updateCategory(categoryId: string, updates: any): Promise<SyncResult> {
    try {
      const { data, error } = await supabase
        .from('categories')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', categoryId)
        .select()
        .single();

      if (error) throw error;

      // Notify subscribers
      this.notifySubscribers('categories', { action: 'update', data });

      return { success: true, data };
    } catch (error: any) {
      console.error('Error updating category:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Delete category with full synchronization
   */
  async deleteCategory(categoryId: string): Promise<SyncResult> {
    try {
      const { data, error } = await supabase
        .from('categories')
        .update({ is_active: false })
        .eq('id', categoryId)
        .select()
        .single();

      if (error) throw error;

      // Notify subscribers
      this.notifySubscribers('categories', { action: 'delete', data });

      return { success: true, data };
    } catch (error: any) {
      console.error('Error deleting category:', error);
      return { success: false, error: error.message };
    }
  }

  // ==================== BRANCH OPERATIONS ====================

  /**
   * Create branch with full synchronization
   */
  async createBranch(branchData: {
    name: string;
    code: string;
    address: string;
    phone: string;
    email?: string;
    manager_id?: string;
    is_main_branch?: boolean;
  }): Promise<SyncResult> {
    try {
      const { data, error } = await supabase
        .from('branches')
        .insert({
          ...branchData,
          is_active: true,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      // Notify subscribers
      this.notifySubscribers('branches', { action: 'create', data });

      return { success: true, data };
    } catch (error: any) {
      console.error('Error creating branch:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Update branch with full synchronization
   */
  async updateBranch(branchId: string, updates: any): Promise<SyncResult> {
    try {
      const { data, error } = await supabase
        .from('branches')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', branchId)
        .select()
        .single();

      if (error) throw error;

      // Notify subscribers
      this.notifySubscribers('branches', { action: 'update', data });

      return { success: true, data };
    } catch (error: any) {
      console.error('Error updating branch:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Delete branch with full synchronization
   */
  async deleteBranch(branchId: string): Promise<SyncResult> {
    try {
      const { data, error } = await supabase
        .from('branches')
        .update({ is_active: false })
        .eq('id', branchId)
        .select()
        .single();

      if (error) throw error;

      // Notify subscribers
      this.notifySubscribers('branches', { action: 'delete', data });

      return { success: true, data };
    } catch (error: any) {
      console.error('Error deleting branch:', error);
      return { success: false, error: error.message };
    }
  }

  // ==================== ORDER OPERATIONS ====================

  /**
   * Create order with full synchronization
   */
  async createOrder(orderData: {
    customer_id: string;
    items: Array<{
      product_id: string;
      quantity: number;
      unit_price: number;
    }>;
    total: number;
    status?: string;
  }): Promise<SyncResult> {
    try {
      // Start transaction
      const { data: order, error: orderError } = await supabase
        .from('orders')
        .insert({
          customer_id: orderData.customer_id,
          order_number: `ORD-${Date.now()}`,
          total: orderData.total,
          status: orderData.status || 'pending',
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (orderError) throw orderError;

      // Create order items
      const orderItems = orderData.items.map(item => ({
        order_id: order.id,
        product_id: item.product_id,
        quantity: item.quantity,
        unit_price: item.unit_price,
        total_price: item.quantity * item.unit_price
      }));

      const { error: itemsError } = await supabase
        .from('order_items')
        .insert(orderItems);

      if (itemsError) throw itemsError;

      // Update product stock
      for (const item of orderData.items) {
        await supabase.rpc('update_product_stock', {
          product_id: item.product_id,
          quantity_change: -item.quantity
        });
      }

      // Notify subscribers
      this.notifySubscribers('orders', { action: 'create', data: order });

      return { success: true, data: order };
    } catch (error: any) {
      console.error('Error creating order:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Update order status with full synchronization
   */
  async updateOrderStatus(orderId: string, status: string): Promise<SyncResult> {
    try {
      const { data, error } = await supabase
        .from('orders')
        .update({
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', orderId)
        .select()
        .single();

      if (error) throw error;

      // Notify subscribers
      this.notifySubscribers('orders', { action: 'update', data });

      return { success: true, data };
    } catch (error: any) {
      console.error('Error updating order:', error);
      return { success: false, error: error.message };
    }
  }

  // ==================== REAL-TIME SETUP ====================

  /**
   * Initialize real-time subscriptions for all tables
   */
  initializeRealTimeSync() {
    const tables = ['users', 'products', 'categories', 'branches', 'orders'];

    tables.forEach(table => {
      try {
        const channel = supabase
          .channel(`${table}-changes`)
          .on('postgres_changes',
            { event: '*', schema: 'public', table },
            (payload) => {
              try {
                console.log(`Real-time change in ${table}:`, payload);
                this.notifySubscribers(table, {
                  action: payload.eventType,
                  data: payload.new || payload.old
                });
              } catch (error) {
                console.error(`Error processing real-time change for ${table}:`, error);
              }
            }
          )
          .subscribe((status) => {
            console.log(`Subscription status for ${table}:`, status);
            if (status === 'SUBSCRIPTION_ERROR') {
              console.error(`Subscription error for ${table}`);
              // Attempt to resubscribe after a delay
              setTimeout(() => {
                console.log(`Attempting to resubscribe to ${table}...`);
                this.initializeTableSubscription(table);
              }, 5000);
            }
          });
      } catch (error) {
        console.error(`Error setting up subscription for ${table}:`, error);
      }
    });
  }

  /**
   * Initialize subscription for a single table (for retry logic)
   */
  private initializeTableSubscription(table: string) {
    try {
      supabase
        .channel(`${table}-changes-retry`)
        .on('postgres_changes',
          { event: '*', schema: 'public', table },
          (payload) => {
            try {
              console.log(`Real-time change in ${table} (retry):`, payload);
              this.notifySubscribers(table, {
                action: payload.eventType,
                data: payload.new || payload.old
              });
            } catch (error) {
              console.error(`Error processing real-time change for ${table} (retry):`, error);
            }
          }
        )
        .subscribe();
    } catch (error) {
      console.error(`Error in retry subscription for ${table}:`, error);
    }
  }
}

// Export singleton instance
export const dataSyncService = DataSyncService.getInstance();
export default dataSyncService;
