-- YalaOffice Supply Management System Database Schema
-- Designed for PostgreSQL (Supabase)
-- Moroccan Market Specific Features Included

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =============================================
-- CORE SYSTEM TABLES
-- =============================================

-- Users table with role-based access control
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    user_type VARCHAR(20) NOT NULL CHECK (user_type IN ('admin', 'manager', 'client', 'reseller', 'delivery_person')),
    phone VARCHAR(20),
    city VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    is_company BOOLEAN DEFAULT false,
    company_name VARCHAR(255),
    ice_number VARCHAR(20), -- Morocco ICE number
    company_address TEXT,
    company_phone VARCHAR(20),
    company_city VARCHAR(100),
    company_email VARCHAR(255),
    tax_id VARCHAR(50),
    legal_form VARCHAR(50), -- SARL, SA, etc.
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login TIMESTAMP WITH TIME ZONE
);

-- User profiles with preferences
CREATE TABLE user_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    avatar_url TEXT,
    bio TEXT,
    job_title VARCHAR(100),
    language VARCHAR(10) DEFAULT 'en',
    currency VARCHAR(10) DEFAULT 'MAD',
    timezone VARCHAR(50) DEFAULT 'Africa/Casablanca',
    notifications JSONB DEFAULT '{"email": true, "sms": true, "push": true, "orderUpdates": true, "promotions": true, "stockAlerts": true}',
    order_defaults JSONB DEFAULT '{"deliveryPreference": "standard"}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Branches/Locations
CREATE TABLE branches (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    address JSONB NOT NULL, -- {street, city, state, postal_code, country}
    contact JSONB NOT NULL, -- {phone, email, manager}
    coordinates JSONB, -- {latitude, longitude}
    is_active BOOLEAN DEFAULT true,
    is_main_branch BOOLEAN DEFAULT false,
    operating_hours JSONB, -- {monday: {open, close, is_closed}, ...}
    services TEXT[],
    capacity JSONB, -- {storage, staff}
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id)
);

-- Categories with hierarchical structure
CREATE TABLE categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    parent_id UUID REFERENCES categories(id),
    level INTEGER DEFAULT 0,
    sort_order INTEGER DEFAULT 0,
    icon VARCHAR(100),
    color VARCHAR(7), -- Hex color
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id)
);

-- Products
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    sku VARCHAR(100) UNIQUE NOT NULL,
    category_id UUID REFERENCES categories(id),
    brand VARCHAR(100),
    price DECIMAL(10,2) NOT NULL,
    reseller_price DECIMAL(10,2),
    cost_price DECIMAL(10,2), -- For profit calculations
    featured_image TEXT,
    thumbnail_images TEXT[],
    rating DECIMAL(3,2) DEFAULT 0,
    review_count INTEGER DEFAULT 0,
    stock INTEGER DEFAULT 0,
    min_stock INTEGER DEFAULT 0,
    max_stock INTEGER,
    reserved_stock INTEGER DEFAULT 0,
    weight DECIMAL(8,3), -- in kg
    dimensions JSONB, -- {length, width, height} in cm
    tags TEXT[],
    is_active BOOLEAN DEFAULT true,
    is_new BOOLEAN DEFAULT false,
    is_featured BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id)
);

-- System configuration
CREATE TABLE system_configs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    category VARCHAR(50) NOT NULL,
    key VARCHAR(100) NOT NULL,
    value JSONB NOT NULL,
    description TEXT,
    data_type VARCHAR(20) NOT NULL CHECK (data_type IN ('string', 'number', 'boolean', 'json', 'array')),
    is_editable BOOLEAN DEFAULT true,
    requires_restart BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by UUID REFERENCES users(id),
    UNIQUE(category, key)
);

-- =============================================
-- INVENTORY MANAGEMENT
-- =============================================

-- Branch-specific inventory
CREATE TABLE branch_inventory (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    branch_id UUID REFERENCES branches(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    stock INTEGER DEFAULT 0,
    min_stock INTEGER DEFAULT 0,
    max_stock INTEGER,
    reserved_stock INTEGER DEFAULT 0,
    last_restocked TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(branch_id, product_id)
);

-- Stock movements tracking
CREATE TABLE stock_movements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID REFERENCES products(id),
    branch_id UUID REFERENCES branches(id),
    movement_type VARCHAR(20) NOT NULL CHECK (movement_type IN ('in', 'out', 'adjustment', 'transfer')),
    quantity INTEGER NOT NULL,
    reason VARCHAR(255),
    reference_id UUID, -- Order ID, Transfer ID, etc.
    reference_type VARCHAR(50), -- 'order', 'transfer', 'adjustment', etc.
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(id)
);

-- Stock transfers between branches
CREATE TABLE stock_transfers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    from_branch_id UUID REFERENCES branches(id),
    to_branch_id UUID REFERENCES branches(id),
    product_id UUID REFERENCES products(id),
    quantity INTEGER NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'in_transit', 'completed', 'cancelled')),
    requested_by UUID REFERENCES users(id),
    approved_by UUID REFERENCES users(id),
    notes TEXT,
    requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    approved_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Low stock alerts
CREATE TABLE low_stock_alerts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID REFERENCES products(id),
    branch_id UUID REFERENCES branches(id),
    current_stock INTEGER NOT NULL,
    min_stock INTEGER NOT NULL,
    is_resolved BOOLEAN DEFAULT false,
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolved_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- ORDER MANAGEMENT
-- =============================================

-- Orders
CREATE TABLE orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_number VARCHAR(50) UNIQUE NOT NULL,
    customer_id UUID REFERENCES users(id),
    branch_id UUID REFERENCES branches(id),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'preparing', 'ready', 'shipped', 'delivered', 'cancelled', 'returned')),
    payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'processing', 'completed', 'failed', 'refunded', 'cancelled')),
    payment_method VARCHAR(20) CHECK (payment_method IN ('cash', 'bank_transfer', 'check', 'credit')),
    subtotal DECIMAL(10,2) NOT NULL,
    delivery_fee DECIMAL(10,2) DEFAULT 0,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    total DECIMAL(10,2) NOT NULL,
    delivery_address JSONB,
    billing_address JSONB,
    promo_code VARCHAR(50),
    notes TEXT,
    estimated_delivery TIMESTAMP WITH TIME ZONE,
    tracking_number VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(id)
);

-- Order items
CREATE TABLE order_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id),
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Order tracking/status history
CREATE TABLE order_tracking (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
    status VARCHAR(20) NOT NULL,
    location VARCHAR(255),
    notes TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by UUID REFERENCES users(id)
);

-- Order templates for recurring orders
CREATE TABLE order_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    customer_id UUID REFERENCES users(id),
    items JSONB NOT NULL, -- [{product_id, quantity}, ...]
    is_active BOOLEAN DEFAULT true,
    last_used TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- PAYMENT MANAGEMENT
-- =============================================

-- Payments
CREATE TABLE payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID REFERENCES orders(id),
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'MAD',
    method VARCHAR(20) NOT NULL CHECK (method IN ('cash', 'bank_transfer', 'check', 'credit')),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'refunded', 'cancelled')),
    transaction_id VARCHAR(255),
    gateway VARCHAR(50),
    gateway_transaction_id VARCHAR(255),
    processed_at TIMESTAMP WITH TIME ZONE,
    failure_reason TEXT,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Payment transactions (for tracking payment history)
CREATE TABLE payment_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    payment_id UUID REFERENCES payments(id),
    transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('charge', 'refund', 'partial_refund')),
    amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) NOT NULL,
    gateway VARCHAR(50),
    gateway_transaction_id VARCHAR(255),
    processed_at TIMESTAMP WITH TIME ZONE,
    failure_reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Credit accounts for resellers
CREATE TABLE credit_accounts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID REFERENCES users(id) UNIQUE,
    balance DECIMAL(10,2) DEFAULT 0,
    credit_limit DECIMAL(10,2) DEFAULT 0,
    available_credit DECIMAL(10,2) DEFAULT 0,
    currency VARCHAR(10) DEFAULT 'MAD',
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'closed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Credit transactions
CREATE TABLE credit_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    credit_account_id UUID REFERENCES credit_accounts(id),
    transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('credit', 'debit', 'adjustment')),
    amount DECIMAL(10,2) NOT NULL,
    description TEXT,
    reference_id UUID,
    reference_type VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(id)
);

-- =============================================
-- CUSTOMER MANAGEMENT
-- =============================================

-- Customer profiles (extends users table)
CREATE TABLE customer_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE UNIQUE,
    discount_rate DECIMAL(5,2) DEFAULT 0, -- For resellers
    credit_limit DECIMAL(10,2) DEFAULT 0,
    total_orders INTEGER DEFAULT 0,
    total_spent DECIMAL(10,2) DEFAULT 0,
    last_order_date TIMESTAMP WITH TIME ZONE,
    loyalty_points INTEGER DEFAULT 0,
    preferred_payment_method VARCHAR(20),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended', 'pending')),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Wishlists
CREATE TABLE wishlists (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID REFERENCES users(id),
    product_id UUID REFERENCES products(id),
    added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(customer_id, product_id)
);

-- Product reviews
CREATE TABLE product_reviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID REFERENCES products(id),
    customer_id UUID REFERENCES users(id),
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    title VARCHAR(255),
    comment TEXT,
    is_verified BOOLEAN DEFAULT false, -- Purchased product
    helpful_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(product_id, customer_id)
);

-- Customer behavior tracking
CREATE TABLE customer_behavior (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID REFERENCES users(id),
    viewed_products UUID[],
    search_history TEXT[],
    category_preferences JSONB, -- {category_id: frequency}
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- PROMOTIONS & MARKETING
-- =============================================

-- Promo codes
CREATE TABLE promo_codes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    discount_type VARCHAR(20) NOT NULL CHECK (discount_type IN ('percentage', 'fixed')),
    discount_value DECIMAL(10,2) NOT NULL,
    min_order_amount DECIMAL(10,2),
    max_discount DECIMAL(10,2),
    usage_limit INTEGER,
    used_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    valid_from TIMESTAMP WITH TIME ZONE NOT NULL,
    valid_until TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(id)
);

-- Promo code usage tracking
CREATE TABLE promo_code_usage (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    promo_code_id UUID REFERENCES promo_codes(id),
    order_id UUID REFERENCES orders(id),
    customer_id UUID REFERENCES users(id),
    discount_amount DECIMAL(10,2) NOT NULL,
    used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- SUPPLIERS & PROCUREMENT
-- =============================================

-- Suppliers
CREATE TABLE suppliers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    contact_person VARCHAR(255),
    email VARCHAR(255),
    phone VARCHAR(20),
    address JSONB,
    tax_id VARCHAR(50),
    ice_number VARCHAR(20), -- Morocco ICE number
    payment_terms VARCHAR(100),
    credit_limit DECIMAL(10,2),
    is_active BOOLEAN DEFAULT true,
    rating DECIMAL(3,2) DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(id)
);

-- Product suppliers (many-to-many relationship)
CREATE TABLE product_suppliers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID REFERENCES products(id),
    supplier_id UUID REFERENCES suppliers(id),
    supplier_sku VARCHAR(100),
    cost_price DECIMAL(10,2),
    min_order_quantity INTEGER DEFAULT 1,
    lead_time_days INTEGER,
    is_primary BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(product_id, supplier_id)
);

-- Purchase orders
CREATE TABLE purchase_orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    po_number VARCHAR(50) UNIQUE NOT NULL,
    supplier_id UUID REFERENCES suppliers(id),
    branch_id UUID REFERENCES branches(id),
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'sent', 'confirmed', 'partially_received', 'received', 'cancelled')),
    subtotal DECIMAL(10,2) NOT NULL,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    total DECIMAL(10,2) NOT NULL,
    expected_delivery TIMESTAMP WITH TIME ZONE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(id)
);

-- Purchase order items
CREATE TABLE purchase_order_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    purchase_order_id UUID REFERENCES purchase_orders(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id),
    quantity INTEGER NOT NULL,
    unit_cost DECIMAL(10,2) NOT NULL,
    total_cost DECIMAL(10,2) NOT NULL,
    received_quantity INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- ANALYTICS & REPORTING
-- =============================================

-- Analytics events for tracking user behavior
CREATE TABLE analytics_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_type VARCHAR(50) NOT NULL,
    user_id UUID REFERENCES users(id),
    session_id VARCHAR(255),
    properties JSONB,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ip_address INET,
    user_agent TEXT
);

-- Business metrics snapshots
CREATE TABLE business_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    metric_type VARCHAR(50) NOT NULL,
    metric_name VARCHAR(100) NOT NULL,
    value DECIMAL(15,2) NOT NULL,
    unit VARCHAR(20),
    period_start TIMESTAMP WITH TIME ZONE,
    period_end TIMESTAMP WITH TIME ZONE,
    branch_id UUID REFERENCES branches(id),
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- SYSTEM ADMINISTRATION
-- =============================================

-- Audit logs for compliance and security
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    entity_id UUID,
    changes JSONB,
    ip_address INET,
    user_agent TEXT,
    severity VARCHAR(20) DEFAULT 'low' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- System notifications
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type VARCHAR(20) DEFAULT 'info' CHECK (type IN ('info', 'success', 'warning', 'error')),
    is_read BOOLEAN DEFAULT false,
    action_url TEXT,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE
);

-- Backup jobs tracking
CREATE TABLE backup_jobs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    job_type VARCHAR(20) NOT NULL CHECK (job_type IN ('full', 'incremental', 'differential')),
    status VARCHAR(20) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'running', 'completed', 'failed')),
    file_name VARCHAR(255),
    file_size BIGINT,
    duration INTEGER, -- seconds
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    schedule JSONB, -- {frequency, time, retention}
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- INTEGRATIONS & API
-- =============================================

-- API endpoints configuration
CREATE TABLE api_endpoints (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    path VARCHAR(255) NOT NULL,
    method VARCHAR(10) NOT NULL,
    description TEXT,
    authentication VARCHAR(20) DEFAULT 'api_key',
    parameters JSONB,
    responses JSONB,
    is_active BOOLEAN DEFAULT true,
    rate_limit INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Webhooks
CREATE TABLE webhooks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    url TEXT NOT NULL,
    events TEXT[] NOT NULL,
    secret VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    retry_attempts INTEGER DEFAULT 3,
    timeout INTEGER DEFAULT 30,
    headers JSONB,
    last_triggered TIMESTAMP WITH TIME ZONE,
    success_count INTEGER DEFAULT 0,
    failure_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ERP integrations
CREATE TABLE erp_integrations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    integration_type VARCHAR(50) NOT NULL,
    connection_string TEXT,
    is_connected BOOLEAN DEFAULT false,
    last_sync TIMESTAMP WITH TIME ZONE,
    sync_frequency VARCHAR(20) DEFAULT 'daily',
    mappings JSONB,
    settings JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- AUTOMATION & WORKFLOWS
-- =============================================

-- Automation rules
CREATE TABLE automation_rules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    trigger_type VARCHAR(50) NOT NULL, -- 'low_stock', 'order_status', 'schedule', etc.
    trigger_conditions JSONB NOT NULL,
    action_type VARCHAR(50) NOT NULL, -- 'reorder', 'notify', 'update_status', etc.
    action_config JSONB NOT NULL,
    is_active BOOLEAN DEFAULT true,
    last_triggered TIMESTAMP WITH TIME ZONE,
    trigger_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(id)
);

-- Stock reorder rules
CREATE TABLE stock_reorder_rules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID REFERENCES products(id),
    branch_id UUID REFERENCES branches(id),
    supplier_id UUID REFERENCES suppliers(id),
    min_stock_level INTEGER NOT NULL,
    reorder_quantity INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT true,
    last_triggered TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(product_id, branch_id)
);

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- Users indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_user_type ON users(user_type);
CREATE INDEX idx_users_is_active ON users(is_active);
CREATE INDEX idx_users_ice_number ON users(ice_number) WHERE ice_number IS NOT NULL;

-- Products indexes
CREATE INDEX idx_products_sku ON products(sku);
CREATE INDEX idx_products_category_id ON products(category_id);
CREATE INDEX idx_products_brand ON products(brand);
CREATE INDEX idx_products_is_active ON products(is_active);
CREATE INDEX idx_products_price ON products(price);
CREATE INDEX idx_products_stock ON products(stock);
CREATE INDEX idx_products_created_at ON products(created_at);
CREATE INDEX idx_products_tags ON products USING GIN(tags);

-- Orders indexes
CREATE INDEX idx_orders_customer_id ON orders(customer_id);
CREATE INDEX idx_orders_branch_id ON orders(branch_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_payment_status ON orders(payment_status);
CREATE INDEX idx_orders_created_at ON orders(created_at);
CREATE INDEX idx_orders_order_number ON orders(order_number);

-- Order items indexes
CREATE INDEX idx_order_items_order_id ON order_items(order_id);
CREATE INDEX idx_order_items_product_id ON order_items(product_id);

-- Inventory indexes
CREATE INDEX idx_branch_inventory_branch_id ON branch_inventory(branch_id);
CREATE INDEX idx_branch_inventory_product_id ON branch_inventory(product_id);
CREATE INDEX idx_branch_inventory_stock ON branch_inventory(stock);

-- Stock movements indexes
CREATE INDEX idx_stock_movements_product_id ON stock_movements(product_id);
CREATE INDEX idx_stock_movements_branch_id ON stock_movements(branch_id);
CREATE INDEX idx_stock_movements_created_at ON stock_movements(created_at);
CREATE INDEX idx_stock_movements_reference ON stock_movements(reference_id, reference_type);

-- Categories indexes
CREATE INDEX idx_categories_parent_id ON categories(parent_id);
CREATE INDEX idx_categories_is_active ON categories(is_active);
CREATE INDEX idx_categories_level ON categories(level);

-- Analytics indexes
CREATE INDEX idx_analytics_events_user_id ON analytics_events(user_id);
CREATE INDEX idx_analytics_events_event_type ON analytics_events(event_type);
CREATE INDEX idx_analytics_events_timestamp ON analytics_events(timestamp);

-- Audit logs indexes
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_entity_type ON audit_logs(entity_type);
CREATE INDEX idx_audit_logs_timestamp ON audit_logs(timestamp);
CREATE INDEX idx_audit_logs_severity ON audit_logs(severity);

-- =============================================
-- TRIGGERS FOR AUTOMATION
-- =============================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers to relevant tables
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_branches_updated_at BEFORE UPDATE ON branches FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_customer_profiles_updated_at BEFORE UPDATE ON customer_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_suppliers_updated_at BEFORE UPDATE ON suppliers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to update product stock and create stock movement
CREATE OR REPLACE FUNCTION update_product_stock()
RETURNS TRIGGER AS $$
BEGIN
    -- Update main product stock
    UPDATE products
    SET stock = stock + NEW.quantity,
        updated_at = NOW()
    WHERE id = NEW.product_id;

    -- Update branch inventory if branch_id is provided
    IF NEW.branch_id IS NOT NULL THEN
        INSERT INTO branch_inventory (branch_id, product_id, stock, updated_at)
        VALUES (NEW.branch_id, NEW.product_id, NEW.quantity, NOW())
        ON CONFLICT (branch_id, product_id)
        DO UPDATE SET
            stock = branch_inventory.stock + NEW.quantity,
            updated_at = NOW();
    END IF;

    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger for stock movements
CREATE TRIGGER trigger_update_product_stock
    AFTER INSERT ON stock_movements
    FOR EACH ROW
    EXECUTE FUNCTION update_product_stock();

-- =============================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =============================================

-- Enable RLS on sensitive tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE credit_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- Users can only see their own profile
CREATE POLICY users_own_profile ON users
    FOR ALL USING (auth.uid() = id);

-- User profiles policy
CREATE POLICY user_profiles_own ON user_profiles
    FOR ALL USING (auth.uid() = user_id);

-- Orders policy - customers can only see their own orders
CREATE POLICY orders_customer_own ON orders
    FOR SELECT USING (
        auth.uid() = customer_id OR
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND user_type IN ('admin', 'manager')
        )
    );

-- Customer profiles policy
CREATE POLICY customer_profiles_own ON customer_profiles
    FOR ALL USING (
        auth.uid() = user_id OR
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND user_type IN ('admin', 'manager')
        )
    );

-- =============================================
-- INITIAL SYSTEM DATA
-- =============================================

-- Insert default system configurations
INSERT INTO system_configs (category, key, value, description, data_type) VALUES
('general', 'app_name', '"YalaOffice"', 'Application name', 'string'),
('general', 'app_version', '"1.0.0"', 'Application version', 'string'),
('general', 'default_currency', '"MAD"', 'Default currency', 'string'),
('general', 'default_timezone', '"Africa/Casablanca"', 'Default timezone', 'string'),
('general', 'default_language', '"en"', 'Default language', 'string'),
('security', 'session_timeout', '3600', 'Session timeout in seconds', 'number'),
('security', 'password_min_length', '8', 'Minimum password length', 'number'),
('security', 'max_login_attempts', '5', 'Maximum login attempts', 'number'),
('notifications', 'email_notifications', 'true', 'Enable email notifications', 'boolean'),
('notifications', 'sms_notifications', 'true', 'Enable SMS notifications', 'boolean'),
('inventory', 'low_stock_threshold', '10', 'Default low stock threshold', 'number'),
('inventory', 'auto_reorder', 'false', 'Enable automatic reordering', 'boolean'),
('orders', 'default_delivery_fee', '30.00', 'Default delivery fee in MAD', 'number'),
('orders', 'free_delivery_threshold', '200.00', 'Free delivery threshold in MAD', 'number');

-- Insert default categories
INSERT INTO categories (id, name, description, level, sort_order, is_active, created_at) VALUES
(uuid_generate_v4(), 'Office Supplies', 'General office supplies and stationery', 0, 1, true, NOW()),
(uuid_generate_v4(), 'School Supplies', 'Educational materials and school stationery', 0, 2, true, NOW()),
(uuid_generate_v4(), 'Technology', 'Computer accessories and tech equipment', 0, 3, true, NOW()),
(uuid_generate_v4(), 'Furniture', 'Office and school furniture', 0, 4, true, NOW()),
(uuid_generate_v4(), 'Cleaning Supplies', 'Cleaning and maintenance products', 0, 5, true, NOW());

-- Insert main branch
INSERT INTO branches (id, name, code, address, contact, is_active, is_main_branch, created_at) VALUES
(uuid_generate_v4(), 'Main Branch - Casablanca', 'MAIN-CASA',
'{"street": "123 Mohammed V Avenue", "city": "Casablanca", "state": "Grand Casablanca", "postal_code": "20000", "country": "Morocco"}',
'{"phone": "+212 522 123 456", "email": "<EMAIL>", "manager": "Store Manager"}',
true, true, NOW());

-- =============================================
-- VIEWS FOR COMMON QUERIES
-- =============================================

-- Product inventory view with branch details
CREATE VIEW product_inventory_view AS
SELECT
    p.id,
    p.title,
    p.sku,
    p.price,
    p.reseller_price,
    c.name as category_name,
    p.stock as total_stock,
    p.min_stock,
    COALESCE(bi.stock, 0) as branch_stock,
    b.name as branch_name,
    b.code as branch_code,
    CASE
        WHEN COALESCE(bi.stock, p.stock) <= p.min_stock THEN 'low'
        WHEN COALESCE(bi.stock, p.stock) = 0 THEN 'out'
        ELSE 'normal'
    END as stock_status
FROM products p
LEFT JOIN categories c ON p.category_id = c.id
LEFT JOIN branch_inventory bi ON p.id = bi.product_id
LEFT JOIN branches b ON bi.branch_id = b.id
WHERE p.is_active = true;

-- Order summary view
CREATE VIEW order_summary_view AS
SELECT
    o.id,
    o.order_number,
    o.status,
    o.payment_status,
    o.total,
    o.created_at,
    u.full_name as customer_name,
    u.email as customer_email,
    b.name as branch_name,
    COUNT(oi.id) as item_count
FROM orders o
JOIN users u ON o.customer_id = u.id
LEFT JOIN branches b ON o.branch_id = b.id
LEFT JOIN order_items oi ON o.id = oi.order_id
GROUP BY o.id, u.full_name, u.email, b.name;

-- Customer analytics view
CREATE VIEW customer_analytics_view AS
SELECT
    u.id,
    u.full_name,
    u.email,
    u.user_type,
    cp.total_orders,
    cp.total_spent,
    cp.last_order_date,
    cp.loyalty_points,
    cp.status,
    CASE
        WHEN cp.total_spent >= 10000 THEN 'VIP'
        WHEN cp.total_spent >= 5000 THEN 'Gold'
        WHEN cp.total_spent >= 1000 THEN 'Silver'
        ELSE 'Bronze'
    END as customer_tier
FROM users u
LEFT JOIN customer_profiles cp ON u.id = cp.user_id
WHERE u.user_type IN ('client', 'reseller');

-- =============================================
-- FUNCTIONS FOR BUSINESS LOGIC
-- =============================================

-- Function to calculate order total
CREATE OR REPLACE FUNCTION calculate_order_total(order_id UUID)
RETURNS DECIMAL(10,2) AS $$
DECLARE
    subtotal DECIMAL(10,2);
    delivery_fee DECIMAL(10,2);
    discount DECIMAL(10,2);
    total DECIMAL(10,2);
BEGIN
    -- Calculate subtotal from order items
    SELECT COALESCE(SUM(total_price), 0) INTO subtotal
    FROM order_items WHERE order_id = calculate_order_total.order_id;

    -- Get delivery fee and discount from order
    SELECT o.delivery_fee, o.discount_amount INTO delivery_fee, discount
    FROM orders o WHERE id = calculate_order_total.order_id;

    total := subtotal + COALESCE(delivery_fee, 0) - COALESCE(discount, 0);

    RETURN total;
END;
$$ LANGUAGE plpgsql;

-- Function to check product availability
CREATE OR REPLACE FUNCTION check_product_availability(product_id UUID, branch_id UUID, quantity INTEGER)
RETURNS BOOLEAN AS $$
DECLARE
    available_stock INTEGER;
BEGIN
    IF branch_id IS NULL THEN
        SELECT stock INTO available_stock FROM products WHERE id = product_id;
    ELSE
        SELECT COALESCE(bi.stock, p.stock) INTO available_stock
        FROM products p
        LEFT JOIN branch_inventory bi ON p.id = bi.product_id AND bi.branch_id = check_product_availability.branch_id
        WHERE p.id = product_id;
    END IF;

    RETURN available_stock >= quantity;
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- COMMENTS FOR DOCUMENTATION
-- =============================================

COMMENT ON TABLE users IS 'Core user accounts with role-based access control';
COMMENT ON TABLE products IS 'Product catalog with inventory tracking';
COMMENT ON TABLE orders IS 'Customer orders with status tracking';
COMMENT ON TABLE branches IS 'Physical locations/branches for multi-location support';
COMMENT ON TABLE categories IS 'Hierarchical product categorization';
COMMENT ON TABLE stock_movements IS 'Audit trail for all inventory changes';
COMMENT ON TABLE audit_logs IS 'System audit trail for compliance and security';

-- Schema creation completed successfully
-- Total tables: 35+
-- Features: Multi-branch, Role-based access, Inventory management, Order processing,
--          Customer management, Analytics, Integrations, Audit trails, Automation
